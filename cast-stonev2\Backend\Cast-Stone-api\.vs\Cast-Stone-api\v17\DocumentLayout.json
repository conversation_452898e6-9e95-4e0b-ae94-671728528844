{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\domain\\models\\status.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:domain\\models\\status.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\domain\\models\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:domain\\models\\user.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\appsettings.production.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:appsettings.production.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\controllers\\smtpcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:controllers\\smtpcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\services\\emailservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:services\\emailservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|c:\\users\\<USER>\\desktop\\cast-stonev2\\cast-stonev2\\backend\\cast-stone-api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{80BBD0B6-5887-4A5F-81C9-7E3E54ECCE57}|Cast-Stone-api.csproj|solutionrelative:properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 5, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "Status.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\Status.cs", "RelativeDocumentMoniker": "Domain\\Models\\Status.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\Status.cs", "RelativeToolTip": "Domain\\Models\\Status.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:28:04.062Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "User.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\User.cs", "RelativeDocumentMoniker": "Domain\\Models\\User.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Domain\\Models\\User.cs", "RelativeToolTip": "Domain\\Models\\User.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAA8AAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T15:27:32.636Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "appsettings.Production.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\appsettings.Production.json", "RelativeDocumentMoniker": "appsettings.Production.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\appsettings.Production.json", "RelativeToolTip": "appsettings.Production.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-21T15:20:38.195Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "launchSettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "Properties\\launchSettings.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Properties\\launchSettings.json", "RelativeToolTip": "Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-16T20:41:38.653Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\appsettings.Development.json", "RelativeDocumentMoniker": "appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\appsettings.Development.json", "RelativeToolTip": "appsettings.Development.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-16T18:59:37.019Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\appsettings.json", "RelativeDocumentMoniker": "appsettings.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\appsettings.json", "RelativeToolTip": "appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-16T18:58:28.739Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "EmailService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Services\\EmailService.cs", "RelativeDocumentMoniker": "Services\\EmailService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Services\\EmailService.cs", "RelativeToolTip": "Services\\EmailService.cs", "ViewState": "AgIAAMcBAAAAAAAAAAAuwNMBAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T18:56:50.26Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "SMTPController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Controllers\\SMTPController.cs", "RelativeDocumentMoniker": "Controllers\\SMTPController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\cast-stonev2\\cast-stonev2\\Backend\\Cast-Stone-api\\Controllers\\SMTPController.cs", "RelativeToolTip": "Controllers\\SMTPController.cs", "ViewState": "AgIAAB4AAAAAAAAAAAAUwCkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-16T18:56:14.243Z"}]}, {"DockedWidth": 68, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}]}]}]}