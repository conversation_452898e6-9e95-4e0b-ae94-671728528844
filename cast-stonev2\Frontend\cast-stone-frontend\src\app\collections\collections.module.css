.container {
  min-height: 100vh;
  background: #1a1a2e;
  color: white;
  padding: 6rem 1rem 2rem;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: white;
}

.subtitle {
  font-size: 1.2rem;
  color: #b0b0b0;
  line-height: 1.6;
}

.loading,
.error {
  text-align: center;
  padding: 3rem;
  font-size: 1.2rem;
}

.error {
  color: #ff6b6b;
}

.collectionsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.collectionCard {
  background: #2a2a3e;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
  border: 2px solid transparent;
}

.collectionCard:hover {
  transform: translateY(-5px);
  border-color: #4a90e2;
  box-shadow: 0 10px 30px rgba(74, 144, 226, 0.2);
}

.imageContainer {
  width: 100%;
  height: 250px;
  overflow: hidden;
  position: relative;
}

.collectionImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.collectionCard:hover .collectionImage {
  transform: scale(1.05);
}

.placeholderImage {
  width: 100%;
  height: 100%;
  background: #3a3a4e;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #888;
  font-size: 1rem;
}

.cardContent {
  padding: 1.5rem;
}

.collectionName {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: white;
}

.collectionDescription {
  color: #b0b0b0;
  line-height: 1.5;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.cardFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
}

.viewCollection {
  color: #4a90e2;
  font-weight: 500;
  font-size: 0.9rem;
}

.emptyState {
  text-align: center;
  padding: 4rem 2rem;
  color: #888;
}

.emptyState h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: white;
}

.emptyState p {
  font-size: 1rem;
  color: #b0b0b0;
}

@media (max-width: 768px) {
  .title {
    font-size: 2rem;
  }
  
  .collectionsGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .container {
    padding: 1rem;
  }
}
