{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/products/PatinaSelector/patinaSelector.module.css"], "sourcesContent": ["/* Patina Selector - Smaller Version */\r\n.patinaSelector {\r\n  margin: 1rem 0;\r\n  padding: 1rem;\r\n  border: 1px solid #ddd;\r\n  background: #fafafa;\r\n}\r\n\r\n.selectorHeader {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 1rem;\r\n  padding-bottom: 0.75rem;\r\n  border-bottom: 1px solid #2563eb;\r\n}\r\n\r\n.selectorTitle {\r\n  font-size: 1rem;\r\n  font-weight: 700;\r\n  color: #1f2937;\r\n  margin: 0;\r\n}\r\n\r\n.selectedPatina {\r\n  font-size: 0.85rem;\r\n  font-weight: 600;\r\n  color: #2563eb;\r\n  background: white;\r\n  padding: 0.4rem 0.75rem;\r\n  border: 1px solid #2563eb;\r\n}\r\n\r\n/* Patina Grid */\r\n.patinaGrid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));\r\n  gap: 0.75rem;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.patinaOption {\r\n  background: white;\r\n  border: 1px solid #ddd;\r\n  padding: 0.75rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  text-align: center;\r\n  min-height: 80px;\r\n}\r\n\r\n.patinaOption:hover {\r\n  border-color: #2563eb;\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.1);\r\n}\r\n\r\n.patinaOption.selected {\r\n  border-color: #2563eb;\r\n  border-width: 2px;\r\n  background: #eff6ff;\r\n  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.15);\r\n}\r\n\r\n.patinaColor {\r\n  width: 30px;\r\n  height: 30px;\r\n  border: 1px solid #333;\r\n  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.patinaName {\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n  color: #1f2937;\r\n  line-height: 1.1;\r\n}\r\n\r\n.patinaOption.selected .patinaName {\r\n  font-weight: 700;\r\n}\r\n\r\n/* Patina Note */\r\n.patinaNote {\r\n  background: #fff3cd;\r\n  border: 1px solid #ffeaa7;\r\n  padding: 0.75rem;\r\n  margin-top: 0.75rem;\r\n}\r\n\r\n.patinaNote p {\r\n  margin: 0;\r\n  font-size: 0.8rem;\r\n  color: #856404;\r\n  line-height: 1.3;\r\n}\r\n\r\n.patinaNote strong {\r\n  color: #533f03;\r\n}\r\n\r\n/* Responsive Adjustments */\r\n@media (max-width: 768px) {\r\n  .patinaSelector {\r\n    padding: 0.75rem;\r\n  }\r\n\r\n  .selectorHeader {\r\n    flex-direction: column;\r\n    gap: 0.75rem;\r\n    text-align: center;\r\n  }\r\n\r\n  .patinaGrid {\r\n    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));\r\n    gap: 0.5rem;\r\n  }\r\n\r\n  .patinaOption {\r\n    padding: 0.5rem;\r\n    min-height: 70px;\r\n    gap: 0.4rem;\r\n  }\r\n\r\n  .patinaColor {\r\n    width: 25px;\r\n    height: 25px;\r\n  }\r\n\r\n  .patinaName {\r\n    font-size: 0.7rem;\r\n  }\r\n\r\n  .selectorTitle {\r\n    font-size: 0.95rem;\r\n  }\r\n\r\n  .selectedPatina {\r\n    font-size: 0.8rem;\r\n    padding: 0.35rem 0.6rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .patinaGrid {\r\n    grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));\r\n  }\r\n\r\n  .patinaOption {\r\n    min-height: 60px;\r\n  }\r\n\r\n  .patinaNote {\r\n    padding: 0.5rem;\r\n  }\r\n\r\n  .patinaNote p {\r\n    font-size: 0.75rem;\r\n  }\r\n}\r\n\r\n/* Accessibility */\r\n@media (prefers-reduced-motion: reduce) {\r\n  .patinaOption {\r\n    transition: none;\r\n  }\r\n\r\n  .patinaOption:hover {\r\n    transform: none;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;;;;;;;;;AAcA;;;;;;AAMA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;AAKA;;;;;;;AAOA;;;;;;;AAOA;;;;AAKA;EACE;;;;EAIA;;;;;;EAMA;;;;;EAKA;;;;;;EAMA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;;AAMF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAMF;EACE;;;;EAIA"}}]}