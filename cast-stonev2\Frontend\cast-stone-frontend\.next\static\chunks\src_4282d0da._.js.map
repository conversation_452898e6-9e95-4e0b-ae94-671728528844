{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/cart/CartItem/cartItem.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"cartItem\": \"cartItem-module__IXYF0W__cartItem\",\n  \"collection\": \"cartItem-module__IXYF0W__collection\",\n  \"imageContainer\": \"cartItem-module__IXYF0W__imageContainer\",\n  \"inStock\": \"cartItem-module__IXYF0W__inStock\",\n  \"itemTotal\": \"cartItem-module__IXYF0W__itemTotal\",\n  \"outOfStock\": \"cartItem-module__IXYF0W__outOfStock\",\n  \"priceBreakdown\": \"cartItem-module__IXYF0W__priceBreakdown\",\n  \"priceSection\": \"cartItem-module__IXYF0W__priceSection\",\n  \"productDescription\": \"cartItem-module__IXYF0W__productDescription\",\n  \"productDetails\": \"cartItem-module__IXYF0W__productDetails\",\n  \"productImage\": \"cartItem-module__IXYF0W__productImage\",\n  \"productMeta\": \"cartItem-module__IXYF0W__productMeta\",\n  \"productName\": \"cartItem-module__IXYF0W__productName\",\n  \"quantity\": \"cartItem-module__IXYF0W__quantity\",\n  \"quantityBtn\": \"cartItem-module__IXYF0W__quantityBtn\",\n  \"quantityControls\": \"cartItem-module__IXYF0W__quantityControls\",\n  \"quantityLabel\": \"cartItem-module__IXYF0W__quantityLabel\",\n  \"quantitySection\": \"cartItem-module__IXYF0W__quantitySection\",\n  \"removeBtn\": \"cartItem-module__IXYF0W__removeBtn\",\n  \"removeSection\": \"cartItem-module__IXYF0W__removeSection\",\n  \"removing\": \"cartItem-module__IXYF0W__removing\",\n  \"stockStatus\": \"cartItem-module__IXYF0W__stockStatus\",\n  \"unitPrice\": \"cartItem-module__IXYF0W__unitPrice\",\n  \"updating\": \"cartItem-module__IXYF0W__updating\",\n  \"wholesaleLabel\": \"cartItem-module__IXYF0W__wholesaleLabel\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/cart/CartItem/CartItem.tsx"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\n/* eslint-disable @next/next/no-img-element */\n'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { CartItem as CartItemType } from '@/services/types/entities';\nimport { useCart } from '@/contexts/CartContext';\nimport { useWholesaleAuth } from '@/contexts/WholesaleAuthContext';\nimport styles from './cartItem.module.css';\n\ninterface CartItemProps {\n  item: CartItemType;\n}\n\nconst CartItem: React.FC<CartItemProps> = ({ item }) => {\n  const { updateCartItem, removeFromCart, state } = useCart();\n  const { isApprovedWholesaleBuyer } = useWholesaleAuth();\n  const [isUpdating, setIsUpdating] = useState(false);\n  const [isRemoving, setIsRemoving] = useState(false);\n  const [forceUpdate, setForceUpdate] = useState(0);\n\n  // Force re-render when wholesale status changes\n  useEffect(() => {\n    setForceUpdate(prev => prev + 1);\n  }, [isApprovedWholesaleBuyer]);\n\n  const handleQuantityChange = async (newQuantity: number) => {\n    if (newQuantity < 1 || !item.product) return;\n    \n    if (newQuantity > item.product.stock) {\n      alert(`Only ${item.product.stock} items available in stock`);\n      return;\n    }\n\n    try {\n      setIsUpdating(true);\n      await updateCartItem(item.productId, newQuantity);\n    } catch (error) {\n      console.error('Error updating quantity:', error);\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  const handleRemove = async () => {\n    try {\n      setIsRemoving(true);\n      await removeFromCart(item.productId);\n    } catch (error) {\n      console.error('Error removing item:', error);\n    } finally {\n      setIsRemoving(false);\n    }\n  };\n\n  const formatPrice = (price: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n    }).format(price);\n  };\n\n  if (!item.product) {\n    return null;\n  }\n\n  const mainImage = item.product.images && item.product.images.length > 0 \n    ? item.product.images[0] \n    : '/images/placeholder-product.jpg';\n\n  // Calculate pricing based on wholesale status and backend data\n  const getEffectivePrice = () => {\n    // If backend provided itemTotal, use that (it includes wholesale pricing)\n    if (item.itemTotal && item.itemTotal > 0) {\n      return item.itemTotal / item.quantity;\n    }\n\n    // Fallback: use wholesale price if user is approved wholesale buyer and product has wholesale price\n    if (\n      isApprovedWholesaleBuyer &&\n      item.product &&\n      item.product.wholeSalePrice &&\n      item.product.wholeSalePrice > 0\n    ) {\n      return item.product.wholeSalePrice;\n    }\n\n    // Default to regular price\n    return item.product?.price ?? 0;\n  };\n\n  const unitPrice = getEffectivePrice();\n  const itemTotal = item.itemTotal || (item.quantity * unitPrice);\n  const isWholesalePrice = isApprovedWholesaleBuyer &&\n    ((item.itemTotal && item.itemTotal > 0) ||\n     (item.product.wholeSalePrice && item.product.wholeSalePrice > 0 && unitPrice === item.product.wholeSalePrice));\n\n  return (\n    <div className={styles.cartItem}>\n      {/* Product Image */}\n      <div className={styles.imageContainer}>\n        <img\n          src={mainImage}\n          alt={item.product.name}\n          className={styles.productImage}\n        />\n      </div>\n\n      {/* Product Details */}\n      <div className={styles.productDetails}>\n        <h3 className={styles.productName}>{item.product.name}</h3>\n        \n        {item.product.description && (\n          <p className={styles.productDescription}>\n            {item.product.description.length > 150 \n              ? `${item.product.description.substring(0, 150)}...` \n              : item.product.description}\n          </p>\n        )}\n\n        <div className={styles.productMeta}>\n          <span className={styles.unitPrice}>\n            {formatPrice(unitPrice)} each\n            {isWholesalePrice && (\n              <span className={styles.wholesaleLabel}> (Wholesale)</span>\n            )}\n          </span>\n          {item.product.collection && (\n            <span className={styles.collection}>\n              {item.product.collection.name}\n            </span>\n          )}\n        </div>\n\n        {/* Stock Status */}\n        <div className={styles.stockStatus}>\n          {item.product.stock > 0 ? (\n            <span className={styles.inStock}>\n              {item.product.stock > 10 ? 'In Stock' : `Only ${item.product.stock} left`}\n            </span>\n          ) : (\n            <span className={styles.outOfStock}>Out of Stock</span>\n          )}\n        </div>\n      </div>\n\n      {/* Quantity Controls */}\n      <div className={styles.quantitySection}>\n        <label className={styles.quantityLabel}>Quantity</label>\n        <div className={styles.quantityControls}>\n          <button\n            type=\"button\"\n            onClick={() => handleQuantityChange(item.quantity - 1)}\n            disabled={item.quantity <= 1 || isUpdating || state.isLoading}\n            className={styles.quantityBtn}\n          >\n            -\n          </button>\n          <span className={styles.quantity}>{item.quantity}</span>\n          <button\n            type=\"button\"\n            onClick={() => handleQuantityChange(item.quantity + 1)}\n            disabled={item.quantity >= item.product.stock || isUpdating || state.isLoading}\n            className={styles.quantityBtn}\n          >\n            +\n          </button>\n        </div>\n        {isUpdating && (\n          <span className={styles.updating}>Updating...</span>\n        )}\n      </div>\n\n      {/* Price Section */}\n      <div className={styles.priceSection}>\n        <div className={styles.itemTotal}>\n          {formatPrice(itemTotal)}\n        </div>\n        <div className={styles.priceBreakdown}>\n          {item.quantity} × {formatPrice(unitPrice)}\n        </div>\n      </div>\n\n      {/* Remove Button */}\n      <div className={styles.removeSection}>\n        <button\n          onClick={handleRemove}\n          disabled={isRemoving || state.isLoading}\n          className={styles.removeBtn}\n          title=\"Remove from cart\"\n        >\n          {isRemoving ? (\n            <span className={styles.removing}>...</span>\n          ) : (\n            <svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n              <path d=\"M3 6h18\"/>\n              <path d=\"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\"/>\n              <path d=\"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\"/>\n              <line x1=\"10\" y1=\"11\" x2=\"10\" y2=\"17\"/>\n              <line x1=\"14\" y1=\"11\" x2=\"14\" y2=\"17\"/>\n            </svg>\n          )}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default CartItem;\n"], "names": [], "mappings": "AAAA,oDAAoD,GACpD,4CAA4C;;;;AAG5C;AAEA;AACA;AACA;;;AANA;;;;;AAYA,MAAM,WAAoC,CAAC,EAAE,IAAI,EAAE;;IACjD,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxD,MAAM,EAAE,wBAAwB,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,mBAAgB,AAAD;IACpD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR;sCAAe,CAAA,OAAQ,OAAO;;QAChC;6BAAG;QAAC;KAAyB;IAE7B,MAAM,uBAAuB,OAAO;QAClC,IAAI,cAAc,KAAK,CAAC,KAAK,OAAO,EAAE;QAEtC,IAAI,cAAc,KAAK,OAAO,CAAC,KAAK,EAAE;YACpC,MAAM,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,KAAK,CAAC,yBAAyB,CAAC;YAC3D;QACF;QAEA,IAAI;YACF,cAAc;YACd,MAAM,eAAe,KAAK,SAAS,EAAE;QACvC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,cAAc;YACd,MAAM,eAAe,KAAK,SAAS;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,IAAI,CAAC,KAAK,OAAO,EAAE;QACjB,OAAO;IACT;IAEA,MAAM,YAAY,KAAK,OAAO,CAAC,MAAM,IAAI,KAAK,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,IAClE,KAAK,OAAO,CAAC,MAAM,CAAC,EAAE,GACtB;IAEJ,+DAA+D;IAC/D,MAAM,oBAAoB;QACxB,0EAA0E;QAC1E,IAAI,KAAK,SAAS,IAAI,KAAK,SAAS,GAAG,GAAG;YACxC,OAAO,KAAK,SAAS,GAAG,KAAK,QAAQ;QACvC;QAEA,oGAAoG;QACpG,IACE,4BACA,KAAK,OAAO,IACZ,KAAK,OAAO,CAAC,cAAc,IAC3B,KAAK,OAAO,CAAC,cAAc,GAAG,GAC9B;YACA,OAAO,KAAK,OAAO,CAAC,cAAc;QACpC;QAEA,2BAA2B;QAC3B,OAAO,KAAK,OAAO,EAAE,SAAS;IAChC;IAEA,MAAM,YAAY;IAClB,MAAM,YAAY,KAAK,SAAS,IAAK,KAAK,QAAQ,GAAG;IACrD,MAAM,mBAAmB,4BACvB,CAAC,AAAC,KAAK,SAAS,IAAI,KAAK,SAAS,GAAG,KACnC,KAAK,OAAO,CAAC,cAAc,IAAI,KAAK,OAAO,CAAC,cAAc,GAAG,KAAK,cAAc,KAAK,OAAO,CAAC,cAAc,AAAC;IAEhH,qBACE,6LAAC;QAAI,WAAW,gKAAA,CAAA,UAAM,CAAC,QAAQ;;0BAE7B,6LAAC;gBAAI,WAAW,gKAAA,CAAA,UAAM,CAAC,cAAc;0BACnC,cAAA,6LAAC;oBACC,KAAK;oBACL,KAAK,KAAK,OAAO,CAAC,IAAI;oBACtB,WAAW,gKAAA,CAAA,UAAM,CAAC,YAAY;;;;;;;;;;;0BAKlC,6LAAC;gBAAI,WAAW,gKAAA,CAAA,UAAM,CAAC,cAAc;;kCACnC,6LAAC;wBAAG,WAAW,gKAAA,CAAA,UAAM,CAAC,WAAW;kCAAG,KAAK,OAAO,CAAC,IAAI;;;;;;oBAEpD,KAAK,OAAO,CAAC,WAAW,kBACvB,6LAAC;wBAAE,WAAW,gKAAA,CAAA,UAAM,CAAC,kBAAkB;kCACpC,KAAK,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG,MAC/B,GAAG,KAAK,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,GAClD,KAAK,OAAO,CAAC,WAAW;;;;;;kCAIhC,6LAAC;wBAAI,WAAW,gKAAA,CAAA,UAAM,CAAC,WAAW;;0CAChC,6LAAC;gCAAK,WAAW,gKAAA,CAAA,UAAM,CAAC,SAAS;;oCAC9B,YAAY;oCAAW;oCACvB,kCACC,6LAAC;wCAAK,WAAW,gKAAA,CAAA,UAAM,CAAC,cAAc;kDAAE;;;;;;;;;;;;4BAG3C,KAAK,OAAO,CAAC,UAAU,kBACtB,6LAAC;gCAAK,WAAW,gKAAA,CAAA,UAAM,CAAC,UAAU;0CAC/B,KAAK,OAAO,CAAC,UAAU,CAAC,IAAI;;;;;;;;;;;;kCAMnC,6LAAC;wBAAI,WAAW,gKAAA,CAAA,UAAM,CAAC,WAAW;kCAC/B,KAAK,OAAO,CAAC,KAAK,GAAG,kBACpB,6LAAC;4BAAK,WAAW,gKAAA,CAAA,UAAM,CAAC,OAAO;sCAC5B,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,aAAa,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;;;;;iDAG3E,6LAAC;4BAAK,WAAW,gKAAA,CAAA,UAAM,CAAC,UAAU;sCAAE;;;;;;;;;;;;;;;;;0BAM1C,6LAAC;gBAAI,WAAW,gKAAA,CAAA,UAAM,CAAC,eAAe;;kCACpC,6LAAC;wBAAM,WAAW,gKAAA,CAAA,UAAM,CAAC,aAAa;kCAAE;;;;;;kCACxC,6LAAC;wBAAI,WAAW,gKAAA,CAAA,UAAM,CAAC,gBAAgB;;0CACrC,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,qBAAqB,KAAK,QAAQ,GAAG;gCACpD,UAAU,KAAK,QAAQ,IAAI,KAAK,cAAc,MAAM,SAAS;gCAC7D,WAAW,gKAAA,CAAA,UAAM,CAAC,WAAW;0CAC9B;;;;;;0CAGD,6LAAC;gCAAK,WAAW,gKAAA,CAAA,UAAM,CAAC,QAAQ;0CAAG,KAAK,QAAQ;;;;;;0CAChD,6LAAC;gCACC,MAAK;gCACL,SAAS,IAAM,qBAAqB,KAAK,QAAQ,GAAG;gCACpD,UAAU,KAAK,QAAQ,IAAI,KAAK,OAAO,CAAC,KAAK,IAAI,cAAc,MAAM,SAAS;gCAC9E,WAAW,gKAAA,CAAA,UAAM,CAAC,WAAW;0CAC9B;;;;;;;;;;;;oBAIF,4BACC,6LAAC;wBAAK,WAAW,gKAAA,CAAA,UAAM,CAAC,QAAQ;kCAAE;;;;;;;;;;;;0BAKtC,6LAAC;gBAAI,WAAW,gKAAA,CAAA,UAAM,CAAC,YAAY;;kCACjC,6LAAC;wBAAI,WAAW,gKAAA,CAAA,UAAM,CAAC,SAAS;kCAC7B,YAAY;;;;;;kCAEf,6LAAC;wBAAI,WAAW,gKAAA,CAAA,UAAM,CAAC,cAAc;;4BAClC,KAAK,QAAQ;4BAAC;4BAAI,YAAY;;;;;;;;;;;;;0BAKnC,6LAAC;gBAAI,WAAW,gKAAA,CAAA,UAAM,CAAC,aAAa;0BAClC,cAAA,6LAAC;oBACC,SAAS;oBACT,UAAU,cAAc,MAAM,SAAS;oBACvC,WAAW,gKAAA,CAAA,UAAM,CAAC,SAAS;oBAC3B,OAAM;8BAEL,2BACC,6LAAC;wBAAK,WAAW,gKAAA,CAAA,UAAM,CAAC,QAAQ;kCAAE;;;;;6CAElC,6LAAC;wBAAI,SAAQ;wBAAY,MAAK;wBAAO,QAAO;;0CAC1C,6LAAC;gCAAK,GAAE;;;;;;0CACR,6LAAC;gCAAK,GAAE;;;;;;0CACR,6LAAC;gCAAK,GAAE;;;;;;0CACR,6LAAC;gCAAK,IAAG;gCAAK,IAAG;gCAAK,IAAG;gCAAK,IAAG;;;;;;0CACjC,6LAAC;gCAAK,IAAG;gCAAK,IAAG;gCAAK,IAAG;gCAAK,IAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C;GAhMM;;QAC8C,kIAAA,CAAA,UAAO;QACpB,2IAAA,CAAA,mBAAgB;;;KAFjD;uCAkMS", "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/cart/CartSummary/cartSummary.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"actionButtons\": \"cartSummary-module__kqlNYa__actionButtons\",\n  \"cartSummary\": \"cartSummary-module__kqlNYa__cartSummary\",\n  \"checkoutBtn\": \"cartSummary-module__kqlNYa__checkoutBtn\",\n  \"checkoutIcon\": \"cartSummary-module__kqlNYa__checkoutIcon\",\n  \"clearBtn\": \"cartSummary-module__kqlNYa__clearBtn\",\n  \"clearIcon\": \"cartSummary-module__kqlNYa__clearIcon\",\n  \"divider\": \"cartSummary-module__kqlNYa__divider\",\n  \"freeShipping\": \"cartSummary-module__kqlNYa__freeShipping\",\n  \"infoIcon\": \"cartSummary-module__kqlNYa__infoIcon\",\n  \"label\": \"cartSummary-module__kqlNYa__label\",\n  \"securityIcon\": \"cartSummary-module__kqlNYa__securityIcon\",\n  \"securityNotice\": \"cartSummary-module__kqlNYa__securityNotice\",\n  \"shippingNotice\": \"cartSummary-module__kqlNYa__shippingNotice\",\n  \"summaryDetails\": \"cartSummary-module__kqlNYa__summaryDetails\",\n  \"summaryRow\": \"cartSummary-module__kqlNYa__summaryRow\",\n  \"title\": \"cartSummary-module__kqlNYa__title\",\n  \"totalLabel\": \"cartSummary-module__kqlNYa__totalLabel\",\n  \"totalRow\": \"cartSummary-module__kqlNYa__totalRow\",\n  \"totalValue\": \"cartSummary-module__kqlNYa__totalValue\",\n  \"value\": \"cartSummary-module__kqlNYa__value\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/cart/CartSummary/CartSummary.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { useCart } from '@/contexts/CartContext';\nimport styles from './cartSummary.module.css';\n\ninterface CartSummaryProps {\n  showCheckoutButton?: boolean;\n  showClearButton?: boolean;\n}\n\nconst CartSummary: React.FC<CartSummaryProps> = ({\n  showCheckoutButton = true,\n  showClearButton = true,\n}) => {\n  const { state, clearCart } = useCart();\n\n  const formatPrice = (price: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n    }).format(price);\n  };\n\n  const handleClearCart = async () => {\n    if (window.confirm('Are you sure you want to clear your cart?')) {\n      await clearCart();\n    }\n  };\n\n  if (!state.cart || state.cart.cartItems.length === 0) {\n    return null;\n  }\n\n  const subtotal = state.cart.totalAmount;\n  const tax = subtotal * 0.08; // 8% tax rate\n  const shipping = subtotal > 100 ? 0 : 15; // Free shipping over $100\n  const total = subtotal + tax + shipping;\n\n  return (\n    <div className={styles.cartSummary}>\n      <h2 className={styles.title}>Order Summary</h2>\n      \n      <div className={styles.summaryDetails}>\n        {/* Items Count */}\n        <div className={styles.summaryRow}>\n          <span className={styles.label}>Items ({state.cart.totalItems})</span>\n          <span className={styles.value}>{formatPrice(subtotal)}</span>\n        </div>\n\n        {/* Shipping */}\n        <div className={styles.summaryRow}>\n          <span className={styles.label}>\n            Shipping\n            {shipping === 0 && <span className={styles.freeShipping}> (Free)</span>}\n          </span>\n          <span className={styles.value}>\n            {shipping === 0 ? 'Free' : formatPrice(shipping)}\n          </span>\n        </div>\n\n        {/* Tax */}\n        <div className={styles.summaryRow}>\n          <span className={styles.label}>Tax</span>\n          <span className={styles.value}>{formatPrice(tax)}</span>\n        </div>\n\n        {/* Divider */}\n        <div className={styles.divider}></div>\n\n        {/* Total */}\n        <div className={styles.totalRow}>\n          <span className={styles.totalLabel}>Total</span>\n          <span className={styles.totalValue}>{formatPrice(total)}</span>\n        </div>\n      </div>\n\n      {/* Free Shipping Notice */}\n      {shipping > 0 && (\n        <div className={styles.shippingNotice}>\n          <svg className={styles.infoIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n            <circle cx=\"12\" cy=\"12\" r=\"10\"/>\n            <path d=\"M12 16v-4\"/>\n            <path d=\"M12 8h.01\"/>\n          </svg>\n          <span>\n            Add {formatPrice(100 - subtotal)} more for free shipping\n          </span>\n        </div>\n      )}\n\n      {/* Action Buttons */}\n      <div className={styles.actionButtons}>\n        {showCheckoutButton && (\n          <Link href=\"/checkout\" className={styles.checkoutBtn}>\n            <svg className={styles.checkoutIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n              <path d=\"M9 12l2 2 4-4\"/>\n            </svg>\n            Proceed to Checkout\n          </Link>\n        )}\n\n        {showClearButton && (\n          <button\n            onClick={handleClearCart}\n            disabled={state.isLoading}\n            className={styles.clearBtn}\n          >\n            <svg className={styles.clearIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n              <path d=\"M3 6h18\"/>\n              <path d=\"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\"/>\n              <path d=\"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\"/>\n            </svg>\n            Clear Cart\n          </button>\n        )}\n      </div>\n\n      {/* Security Notice */}\n      <div className={styles.securityNotice}>\n        <svg className={styles.securityIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n          <path d=\"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z\"/>\n          <path d=\"M9 12l2 2 4-4\"/>\n        </svg>\n        <span>Secure Checkout</span>\n      </div>\n    </div>\n  );\n};\n\nexport default CartSummary;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;;;AALA;;;;AAYA,MAAM,cAA0C,CAAC,EAC/C,qBAAqB,IAAI,EACzB,kBAAkB,IAAI,EACvB;;IACC,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEnC,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,kBAAkB;QACtB,IAAI,OAAO,OAAO,CAAC,8CAA8C;YAC/D,MAAM;QACR;IACF;IAEA,IAAI,CAAC,MAAM,IAAI,IAAI,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,GAAG;QACpD,OAAO;IACT;IAEA,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW;IACvC,MAAM,MAAM,WAAW,MAAM,cAAc;IAC3C,MAAM,WAAW,WAAW,MAAM,IAAI,IAAI,0BAA0B;IACpE,MAAM,QAAQ,WAAW,MAAM;IAE/B,qBACE,6LAAC;QAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,WAAW;;0BAChC,6LAAC;gBAAG,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;0BAAE;;;;;;0BAE7B,6LAAC;gBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,cAAc;;kCAEnC,6LAAC;wBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,UAAU;;0CAC/B,6LAAC;gCAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;;oCAAE;oCAAQ,MAAM,IAAI,CAAC,UAAU;oCAAC;;;;;;;0CAC7D,6LAAC;gCAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;0CAAG,YAAY;;;;;;;;;;;;kCAI9C,6LAAC;wBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,UAAU;;0CAC/B,6LAAC;gCAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;;oCAAE;oCAE5B,aAAa,mBAAK,6LAAC;wCAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,YAAY;kDAAE;;;;;;;;;;;;0CAE3D,6LAAC;gCAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;0CAC1B,aAAa,IAAI,SAAS,YAAY;;;;;;;;;;;;kCAK3C,6LAAC;wBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,UAAU;;0CAC/B,6LAAC;gCAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;0CAAE;;;;;;0CAC/B,6LAAC;gCAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,KAAK;0CAAG,YAAY;;;;;;;;;;;;kCAI9C,6LAAC;wBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,OAAO;;;;;;kCAG9B,6LAAC;wBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,QAAQ;;0CAC7B,6LAAC;gCAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,UAAU;0CAAE;;;;;;0CACpC,6LAAC;gCAAK,WAAW,sKAAA,CAAA,UAAM,CAAC,UAAU;0CAAG,YAAY;;;;;;;;;;;;;;;;;;YAKpD,WAAW,mBACV,6LAAC;gBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,cAAc;;kCACnC,6LAAC;wBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,QAAQ;wBAAE,SAAQ;wBAAY,MAAK;wBAAO,QAAO;;0CACtE,6LAAC;gCAAO,IAAG;gCAAK,IAAG;gCAAK,GAAE;;;;;;0CAC1B,6LAAC;gCAAK,GAAE;;;;;;0CACR,6LAAC;gCAAK,GAAE;;;;;;;;;;;;kCAEV,6LAAC;;4BAAK;4BACC,YAAY,MAAM;4BAAU;;;;;;;;;;;;;0BAMvC,6LAAC;gBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,aAAa;;oBACjC,oCACC,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAY,WAAW,sKAAA,CAAA,UAAM,CAAC,WAAW;;0CAClD,6LAAC;gCAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,YAAY;gCAAE,SAAQ;gCAAY,MAAK;gCAAO,QAAO;0CAC1E,cAAA,6LAAC;oCAAK,GAAE;;;;;;;;;;;4BACJ;;;;;;;oBAKT,iCACC,6LAAC;wBACC,SAAS;wBACT,UAAU,MAAM,SAAS;wBACzB,WAAW,sKAAA,CAAA,UAAM,CAAC,QAAQ;;0CAE1B,6LAAC;gCAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,SAAS;gCAAE,SAAQ;gCAAY,MAAK;gCAAO,QAAO;;kDACvE,6LAAC;wCAAK,GAAE;;;;;;kDACR,6LAAC;wCAAK,GAAE;;;;;;kDACR,6LAAC;wCAAK,GAAE;;;;;;;;;;;;4BACJ;;;;;;;;;;;;;0BAOZ,6LAAC;gBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,cAAc;;kCACnC,6LAAC;wBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,YAAY;wBAAE,SAAQ;wBAAY,MAAK;wBAAO,QAAO;;0CAC1E,6LAAC;gCAAK,GAAE;;;;;;0CACR,6LAAC;gCAAK,GAAE;;;;;;;;;;;;kCAEV,6LAAC;kCAAK;;;;;;;;;;;;;;;;;;AAId;GArHM;;QAIyB,kIAAA,CAAA,UAAO;;;KAJhC;uCAuHS", "debugId": null}}, {"offset": {"line": 824, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/cart/cart.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"additionalActions\": \"cart-module__-RJi4G__additionalActions\",\n  \"cartContent\": \"cart-module__-RJi4G__cartContent\",\n  \"cartItems\": \"cart-module__-RJi4G__cartItems\",\n  \"contactLink\": \"cart-module__-RJi4G__contactLink\",\n  \"container\": \"cart-module__-RJi4G__container\",\n  \"continueShoppingLink\": \"cart-module__-RJi4G__continueShoppingLink\",\n  \"emptyCart\": \"cart-module__-RJi4G__emptyCart\",\n  \"emptyIcon\": \"cart-module__-RJi4G__emptyIcon\",\n  \"emptyMessage\": \"cart-module__-RJi4G__emptyMessage\",\n  \"emptyTitle\": \"cart-module__-RJi4G__emptyTitle\",\n  \"errorIcon\": \"cart-module__-RJi4G__errorIcon\",\n  \"errorMessage\": \"cart-module__-RJi4G__errorMessage\",\n  \"header\": \"cart-module__-RJi4G__header\",\n  \"helpSection\": \"cart-module__-RJi4G__helpSection\",\n  \"itemsHeader\": \"cart-module__-RJi4G__itemsHeader\",\n  \"itemsList\": \"cart-module__-RJi4G__itemsList\",\n  \"loadingContainer\": \"cart-module__-RJi4G__loadingContainer\",\n  \"loadingSpinner\": \"cart-module__-RJi4G__loadingSpinner\",\n  \"shippingInfo\": \"cart-module__-RJi4G__shippingInfo\",\n  \"shopIcon\": \"cart-module__-RJi4G__shopIcon\",\n  \"shopNowBtn\": \"cart-module__-RJi4G__shopNowBtn\",\n  \"spin\": \"cart-module__-RJi4G__spin\",\n  \"subtitle\": \"cart-module__-RJi4G__subtitle\",\n  \"title\": \"cart-module__-RJi4G__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 856, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/app/cart/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { useCart } from '@/contexts/CartContext';\nimport CartItem from '@/components/cart/CartItem/CartItem';\nimport CartSummary from '@/components/cart/CartSummary/CartSummary';\nimport styles from './cart.module.css';\n\nexport default function CartPage() {\n  const { state } = useCart();\n\n  if (state.isLoading) {\n    return (\n      <div className={styles.container}>\n        <div className={styles.loadingContainer}>\n          <div className={styles.loadingSpinner}></div>\n          <p>Loading your cart...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!state.cart || state.cart.cartItems.length === 0) {\n    return (\n      <div className={styles.container}>\n        <div className={styles.emptyCart}>\n          <div className={styles.emptyIcon}>\n            <svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n              <path d=\"M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17C17 18.1 16.1 19 15 19H9C7.9 19 7 18.1 7 17V13M17 13H7\"/>\n            </svg>\n          </div>\n          <h1 className={styles.emptyTitle}>Your Cart is Empty</h1>\n          <p className={styles.emptyMessage}>\n            Looks like you haven&apos;t added any items to your cart yet. \n            Start shopping to fill it up!\n          </p>\n          <Link href=\"/products\" className={styles.shopNowBtn}>\n            <svg className={styles.shopIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n              <path d=\"M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V17C17 18.1 16.1 19 15 19H9C7.9 19 7 18.1 7 17V13M17 13H7\"/>\n            </svg>\n            Start Shopping\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={styles.container}>\n      {/* Header */}\n      <div className={styles.header}>\n        <h1 className={styles.title}>Shopping Cart</h1>\n        <p className={styles.subtitle}>\n          {state.cart.totalItems} {state.cart.totalItems === 1 ? 'item' : 'items'} in your cart\n        </p>\n      </div>\n\n      {/* Error Message */}\n      {state.error && (\n        <div className={styles.errorMessage}>\n          <svg className={styles.errorIcon} viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n            <circle cx=\"12\" cy=\"12\" r=\"10\"/>\n            <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\"/>\n            <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\"/>\n          </svg>\n          <span>{state.error}</span>\n        </div>\n      )}\n\n      {/* Cart Content */}\n      <div className={styles.cartContent}>\n        {/* Cart Items */}\n        <div className={styles.cartItems}>\n          <div className={styles.itemsHeader}>\n            <h2>Items</h2>\n            <Link href=\"/products\" className={styles.continueShoppingLink}>\n              Continue Shopping\n            </Link>\n          </div>\n          \n          <div className={styles.itemsList}>\n            {state.cart.cartItems.map((item) => (\n              <CartItem key={item.id} item={item} />\n            ))}\n          </div>\n        </div>\n\n        {/* Cart Summary */}\n        <div className={styles.cartSummaryContainer}>\n          <CartSummary />\n        </div>\n      </div>\n\n      {/* Additional Actions */}\n      <div className={styles.additionalActions}>\n        <div className={styles.helpSection}>\n          <h3>Need Help?</h3>\n          <p>\n            Have questions about your order? \n            <Link href=\"/contact\" className={styles.contactLink}> Contact us</Link> \n            or call (555) 123-4567\n          </p>\n        </div>\n\n        <div className={styles.shippingInfo}>\n          <h3>Shipping Information</h3>\n          <ul>\n            <li>Free shipping on orders over $100</li>\n            <li>Standard delivery: 5-7 business days</li>\n            <li>Express delivery: 2-3 business days</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;AASe,SAAS;;IACtB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAExB,IAAI,MAAM,SAAS,EAAE;QACnB,qBACE,6LAAC;YAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;sBAC9B,cAAA,6LAAC;gBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,gBAAgB;;kCACrC,6LAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,cAAc;;;;;;kCACrC,6LAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,IAAI,CAAC,MAAM,IAAI,IAAI,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,GAAG;QACpD,qBACE,6LAAC;YAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;sBAC9B,cAAA,6LAAC;gBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;;kCAC9B,6LAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;kCAC9B,cAAA,6LAAC;4BAAI,SAAQ;4BAAY,MAAK;4BAAO,QAAO;sCAC1C,cAAA,6LAAC;gCAAK,GAAE;;;;;;;;;;;;;;;;kCAGZ,6LAAC;wBAAG,WAAW,yIAAA,CAAA,UAAM,CAAC,UAAU;kCAAE;;;;;;kCAClC,6LAAC;wBAAE,WAAW,yIAAA,CAAA,UAAM,CAAC,YAAY;kCAAE;;;;;;kCAInC,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAY,WAAW,yIAAA,CAAA,UAAM,CAAC,UAAU;;0CACjD,6LAAC;gCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;gCAAE,SAAQ;gCAAY,MAAK;gCAAO,QAAO;0CACtE,cAAA,6LAAC;oCAAK,GAAE;;;;;;;;;;;4BACJ;;;;;;;;;;;;;;;;;;IAMhB;IAEA,qBACE,6LAAC;QAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;;0BAE9B,6LAAC;gBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,MAAM;;kCAC3B,6LAAC;wBAAG,WAAW,yIAAA,CAAA,UAAM,CAAC,KAAK;kCAAE;;;;;;kCAC7B,6LAAC;wBAAE,WAAW,yIAAA,CAAA,UAAM,CAAC,QAAQ;;4BAC1B,MAAM,IAAI,CAAC,UAAU;4BAAC;4BAAE,MAAM,IAAI,CAAC,UAAU,KAAK,IAAI,SAAS;4BAAQ;;;;;;;;;;;;;YAK3E,MAAM,KAAK,kBACV,6LAAC;gBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,YAAY;;kCACjC,6LAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;wBAAE,SAAQ;wBAAY,MAAK;wBAAO,QAAO;;0CACvE,6LAAC;gCAAO,IAAG;gCAAK,IAAG;gCAAK,GAAE;;;;;;0CAC1B,6LAAC;gCAAK,IAAG;gCAAK,IAAG;gCAAI,IAAG;gCAAI,IAAG;;;;;;0CAC/B,6LAAC;gCAAK,IAAG;gCAAI,IAAG;gCAAI,IAAG;gCAAK,IAAG;;;;;;;;;;;;kCAEjC,6LAAC;kCAAM,MAAM,KAAK;;;;;;;;;;;;0BAKtB,6LAAC;gBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;;kCAEhC,6LAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;;0CAC9B,6LAAC;gCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;;kDAChC,6LAAC;kDAAG;;;;;;kDACJ,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAW,yIAAA,CAAA,UAAM,CAAC,oBAAoB;kDAAE;;;;;;;;;;;;0CAKjE,6LAAC;gCAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,SAAS;0CAC7B,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,qBACzB,6LAAC,qJAAA,CAAA,UAAQ;wCAAe,MAAM;uCAAf,KAAK,EAAE;;;;;;;;;;;;;;;;kCAM5B,6LAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,oBAAoB;kCACzC,cAAA,6LAAC,2JAAA,CAAA,UAAW;;;;;;;;;;;;;;;;0BAKhB,6LAAC;gBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,iBAAiB;;kCACtC,6LAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;;0CAChC,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;;oCAAE;kDAED,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAW,yIAAA,CAAA,UAAM,CAAC,WAAW;kDAAE;;;;;;oCAAkB;;;;;;;;;;;;;kCAK3E,6LAAC;wBAAI,WAAW,yIAAA,CAAA,UAAM,CAAC,YAAY;;0CACjC,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;;kDACC,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhB;GA3GwB;;QACJ,kIAAA,CAAA,UAAO;;;KADH", "debugId": null}}]}