/* [project]/src/components/wholesale/WholesaleUserMenu.module.css [app-client] (css) */
.WholesaleUserMenu-module__nuW7AW__wholesaleUserMenu {
  display: inline-block;
  position: relative;
}

.WholesaleUserMenu-module__nuW7AW__userButton {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  border: none;
  border-radius: 8px;
  align-items: center;
  gap: .5rem;
  padding: .5rem 1rem;
  font-size: .875rem;
  font-weight: 500;
  transition: all .2s;
  display: flex;
  box-shadow: 0 2px 4px #05966933;
}

.WholesaleUserMenu-module__nuW7AW__userButton:hover {
  background: linear-gradient(135deg, #047857 0%, #065f46 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px #0596694d;
}

.WholesaleUserMenu-module__nuW7AW__userInfo {
  text-align: left;
  flex-direction: column;
  align-items: flex-start;
  display: flex;
}

.WholesaleUserMenu-module__nuW7AW__wholesaleLabel {
  text-transform: uppercase;
  letter-spacing: .5px;
  opacity: .9;
  font-size: .75rem;
  font-weight: 600;
  line-height: 1;
}

.WholesaleUserMenu-module__nuW7AW__userName {
  margin-top: .125rem;
  font-size: .875rem;
  font-weight: 500;
  line-height: 1.2;
}

.WholesaleUserMenu-module__nuW7AW__chevron {
  opacity: .8;
  transition: transform .2s;
}

.WholesaleUserMenu-module__nuW7AW__chevron.WholesaleUserMenu-module__nuW7AW__open {
  transform: rotate(180deg);
}

.WholesaleUserMenu-module__nuW7AW__dropdown {
  z-index: 1000;
  margin-top: .5rem;
  position: absolute;
  top: 100%;
  right: 0;
}

.WholesaleUserMenu-module__nuW7AW__dropdownContent {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  min-width: 280px;
  animation: .2s ease-out WholesaleUserMenu-module__nuW7AW__dropdownFadeIn;
  overflow: hidden;
  box-shadow: 0 10px 25px #00000026;
}

@keyframes WholesaleUserMenu-module__nuW7AW__dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.WholesaleUserMenu-module__nuW7AW__userDetails {
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
  padding: 1.5rem;
}

.WholesaleUserMenu-module__nuW7AW__userEmail {
  color: #374151;
  margin-bottom: .75rem;
  font-size: .875rem;
  font-weight: 500;
}

.WholesaleUserMenu-module__nuW7AW__statusBadge {
  color: #059669;
  text-transform: uppercase;
  letter-spacing: .5px;
  align-items: center;
  gap: .5rem;
  font-size: .75rem;
  font-weight: 600;
  display: flex;
}

.WholesaleUserMenu-module__nuW7AW__statusDot {
  background: #10b981;
  border-radius: 50%;
  width: 8px;
  height: 8px;
  animation: 2s infinite WholesaleUserMenu-module__nuW7AW__pulse;
}

@keyframes WholesaleUserMenu-module__nuW7AW__pulse {
  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: .5;
  }
}

.WholesaleUserMenu-module__nuW7AW__divider {
  background: #e5e7eb;
  height: 1px;
}

.WholesaleUserMenu-module__nuW7AW__logoutButton {
  color: #6b7280;
  cursor: pointer;
  text-align: left;
  background: none;
  border: none;
  align-items: center;
  gap: .75rem;
  width: 100%;
  padding: 1rem 1.5rem;
  font-size: .875rem;
  font-weight: 500;
  transition: all .2s;
  display: flex;
}

.WholesaleUserMenu-module__nuW7AW__logoutButton:hover {
  color: #ef4444;
  background: #f9fafb;
}

.WholesaleUserMenu-module__nuW7AW__logoutButton svg {
  transition: transform .2s;
}

.WholesaleUserMenu-module__nuW7AW__logoutButton:hover svg {
  transform: translateX(2px);
}

.WholesaleUserMenu-module__nuW7AW__overlay {
  z-index: 999;
  background: none;
  position: fixed;
  inset: 0;
}

@media (width <= 768px) {
  .WholesaleUserMenu-module__nuW7AW__userButton {
    padding: .375rem .75rem;
    font-size: .8125rem;
  }

  .WholesaleUserMenu-module__nuW7AW__wholesaleLabel {
    font-size: .6875rem;
  }

  .WholesaleUserMenu-module__nuW7AW__userName {
    font-size: .8125rem;
  }

  .WholesaleUserMenu-module__nuW7AW__dropdownContent {
    min-width: 260px;
    margin-right: 1rem;
  }

  .WholesaleUserMenu-module__nuW7AW__userDetails {
    padding: 1.25rem;
  }

  .WholesaleUserMenu-module__nuW7AW__logoutButton {
    padding: .875rem 1.25rem;
  }
}

@media (width <= 480px) {
  .WholesaleUserMenu-module__nuW7AW__userInfo {
    display: none;
  }

  .WholesaleUserMenu-module__nuW7AW__userButton {
    border-radius: 50%;
    justify-content: center;
    width: 40px;
    height: 40px;
    padding: .5rem;
  }

  .WholesaleUserMenu-module__nuW7AW__userButton:before {
    content: "W";
    font-size: 1rem;
    font-weight: 700;
  }

  .WholesaleUserMenu-module__nuW7AW__chevron {
    display: none;
  }

  .WholesaleUserMenu-module__nuW7AW__dropdown {
    right: -1rem;
  }

  .WholesaleUserMenu-module__nuW7AW__dropdownContent {
    min-width: 240px;
  }
}

/*# sourceMappingURL=src_components_wholesale_WholesaleUserMenu_module_css_f9ee138c._.single.css.map*/