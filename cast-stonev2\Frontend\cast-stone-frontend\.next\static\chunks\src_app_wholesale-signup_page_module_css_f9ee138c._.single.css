/* [project]/src/app/wholesale-signup/page.module.css [app-client] (css) */
.page-module__7gVXna__pageContainer {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  margin-top: 4.2rem;
}

.page-module__7gVXna__header {
  color: #fff;
  text-align: center;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  padding: 3rem 0;
}

.page-module__7gVXna__headerContent h1 {
  margin: 0 0 1rem;
  font-size: 2.5rem;
  font-weight: 700;
}

.page-module__7gVXna__headerContent p {
  opacity: .9;
  margin: 0;
  font-size: 1.125rem;
}

.page-module__7gVXna__errorBanner {
  color: #dc2626;
  background: #fef2f2;
  border: 1px solid #fecaca;
  justify-content: space-between;
  align-items: center;
  margin: 0;
  padding: 1rem;
  display: flex;
}

.page-module__7gVXna__errorBanner p {
  margin: 0;
  font-weight: 500;
}

.page-module__7gVXna__closeError {
  color: #dc2626;
  cursor: pointer;
  background: none;
  border: none;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  padding: 0;
  font-size: 1.5rem;
  display: flex;
}

.page-module__7gVXna__contentContainer {
  grid-template-columns: 1fr 300px;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  display: grid;
}

.page-module__7gVXna__mainContent {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px #0000001a;
}

.page-module__7gVXna__sidebar {
  background: #fff;
  border-radius: 12px;
  height: fit-content;
  padding: 2rem;
  position: sticky;
  top: 2rem;
  box-shadow: 0 4px 6px #0000001a;
}

.page-module__7gVXna__benefits h3 {
  color: #1f2937;
  margin: 0 0 1.5rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.page-module__7gVXna__benefits ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.page-module__7gVXna__benefits li {
  color: #374151;
  margin-bottom: .75rem;
  padding-left: 1.5rem;
  font-size: .875rem;
  line-height: 1.5;
  position: relative;
}

.page-module__7gVXna__benefits li:before {
  content: "✓";
  color: #10b981;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.page-module__7gVXna__formHeader {
  border-bottom: 1px solid #e5e7eb;
  padding: 2rem 2rem 1rem;
}

.page-module__7gVXna__formHeader h2 {
  color: #1f2937;
  margin: 0 0 .5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.page-module__7gVXna__formHeader p {
  color: #6b7280;
  margin: 0 0 1rem;
  font-size: .875rem;
}

.page-module__7gVXna__backToLogin {
  color: #3b82f6;
  cursor: pointer;
  background: none;
  border: none;
  padding: 0;
  font-size: .875rem;
  text-decoration: underline;
}

.page-module__7gVXna__backToLogin:hover {
  color: #2563eb;
}

.page-module__7gVXna__messageContainer {
  max-width: 600px;
  margin: 2rem auto;
  padding: 0 2rem;
}

.page-module__7gVXna__successMessage, .page-module__7gVXna__pendingMessage {
  text-align: center;
  background: #fff;
  border-radius: 12px;
  padding: 3rem;
  box-shadow: 0 4px 6px #0000001a;
}

.page-module__7gVXna__successIcon, .page-module__7gVXna__pendingIcon {
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  font-size: 2rem;
  display: flex;
}

.page-module__7gVXna__successIcon {
  color: #10b981;
  background: #d1fae5;
}

.page-module__7gVXna__pendingIcon {
  color: #f59e0b;
  background: #fef3c7;
}

.page-module__7gVXna__successMessage h2, .page-module__7gVXna__pendingMessage h2 {
  color: #1f2937;
  margin: 0 0 1rem;
  font-size: 1.75rem;
  font-weight: 600;
}

.page-module__7gVXna__successMessage p, .page-module__7gVXna__pendingMessage p {
  color: #6b7280;
  margin: 0 0 2rem;
  font-size: 1rem;
  line-height: 1.6;
}

.page-module__7gVXna__nextSteps {
  text-align: left;
  background: #f9fafb;
  border-radius: 8px;
  margin: 2rem 0;
  padding: 1.5rem;
}

.page-module__7gVXna__nextSteps h3 {
  color: #1f2937;
  margin: 0 0 1rem;
  font-size: 1.125rem;
  font-weight: 600;
}

.page-module__7gVXna__nextSteps ol {
  color: #374151;
  margin: 0;
  padding-left: 1.5rem;
}

.page-module__7gVXna__nextSteps li {
  margin-bottom: .5rem;
  line-height: 1.5;
}

.page-module__7gVXna__contactInfo {
  background: #f0f9ff;
  border-radius: 8px;
  margin: 2rem 0;
  padding: 1.5rem;
}

.page-module__7gVXna__contactInfo p {
  color: #374151;
  margin: 0;
  font-size: .875rem;
}

.page-module__7gVXna__contactInfo a {
  color: #3b82f6;
  text-decoration: none;
}

.page-module__7gVXna__contactInfo a:hover {
  text-decoration: underline;
}

.page-module__7gVXna__primaryButton {
  color: #fff;
  cursor: pointer;
  background: #3b82f6;
  border: none;
  border-radius: 6px;
  padding: .75rem 2rem;
  font-weight: 600;
  transition: background-color .2s;
}

.page-module__7gVXna__primaryButton:hover {
  background: #2563eb;
}

@media (width <= 1024px) {
  .page-module__7gVXna__contentContainer {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .page-module__7gVXna__sidebar {
    order: -1;
    position: static;
  }
}

@media (width <= 768px) {
  .page-module__7gVXna__header {
    padding: 2rem 1rem;
  }

  .page-module__7gVXna__headerContent h1 {
    font-size: 2rem;
  }

  .page-module__7gVXna__contentContainer {
    padding: 1rem;
  }

  .page-module__7gVXna__sidebar {
    padding: 1.5rem;
  }

  .page-module__7gVXna__formHeader {
    padding: 1.5rem 1.5rem 1rem;
  }

  .page-module__7gVXna__successMessage, .page-module__7gVXna__pendingMessage {
    padding: 2rem;
  }

  .page-module__7gVXna__messageContainer {
    padding: 0 1rem;
  }
}

@media (width <= 480px) {
  .page-module__7gVXna__headerContent h1 {
    font-size: 1.75rem;
  }

  .page-module__7gVXna__headerContent p {
    font-size: 1rem;
  }

  .page-module__7gVXna__successMessage, .page-module__7gVXna__pendingMessage {
    padding: 1.5rem;
  }

  .page-module__7gVXna__successIcon, .page-module__7gVXna__pendingIcon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
}

/*# sourceMappingURL=src_app_wholesale-signup_page_module_css_f9ee138c._.single.css.map*/