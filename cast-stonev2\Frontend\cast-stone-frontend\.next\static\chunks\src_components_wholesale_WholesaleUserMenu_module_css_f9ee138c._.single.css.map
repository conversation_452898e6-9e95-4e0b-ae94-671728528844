{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/wholesale/WholesaleUserMenu.module.css"], "sourcesContent": [".wholesaleUserMenu {\r\n  position: relative;\r\n  display: inline-block;\r\n}\r\n\r\n.userButton {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  padding: 0.5rem 1rem;\r\n  background: linear-gradient(135deg, #059669 0%, #047857 100%);\r\n  color: white;\r\n  border: none;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  box-shadow: 0 2px 4px rgba(5, 150, 105, 0.2);\r\n}\r\n\r\n.userButton:hover {\r\n  background: linear-gradient(135deg, #047857 0%, #065f46 100%);\r\n  box-shadow: 0 4px 8px rgba(5, 150, 105, 0.3);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.userInfo {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  text-align: left;\r\n}\r\n\r\n.wholesaleLabel {\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n  opacity: 0.9;\r\n  line-height: 1;\r\n}\r\n\r\n.userName {\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  line-height: 1.2;\r\n  margin-top: 0.125rem;\r\n}\r\n\r\n.chevron {\r\n  transition: transform 0.2s ease;\r\n  opacity: 0.8;\r\n}\r\n\r\n.chevron.open {\r\n  transform: rotate(180deg);\r\n}\r\n\r\n.dropdown {\r\n  position: absolute;\r\n  top: 100%;\r\n  right: 0;\r\n  margin-top: 0.5rem;\r\n  z-index: 1000;\r\n}\r\n\r\n.dropdownContent {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\r\n  border: 1px solid #e5e7eb;\r\n  min-width: 280px;\r\n  overflow: hidden;\r\n  animation: dropdownFadeIn 0.2s ease-out;\r\n}\r\n\r\n@keyframes dropdownFadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(-10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.userDetails {\r\n  padding: 1.5rem;\r\n  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);\r\n}\r\n\r\n.userEmail {\r\n  color: #374151;\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  margin-bottom: 0.75rem;\r\n}\r\n\r\n.statusBadge {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  color: #059669;\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.statusDot {\r\n  width: 8px;\r\n  height: 8px;\r\n  background: #10b981;\r\n  border-radius: 50%;\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% {\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    opacity: 0.5;\r\n  }\r\n}\r\n\r\n.divider {\r\n  height: 1px;\r\n  background: #e5e7eb;\r\n}\r\n\r\n.logoutButton {\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n  padding: 1rem 1.5rem;\r\n  background: none;\r\n  border: none;\r\n  color: #6b7280;\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  text-align: left;\r\n}\r\n\r\n.logoutButton:hover {\r\n  background: #f9fafb;\r\n  color: #ef4444;\r\n}\r\n\r\n.logoutButton svg {\r\n  transition: transform 0.2s ease;\r\n}\r\n\r\n.logoutButton:hover svg {\r\n  transform: translateX(2px);\r\n}\r\n\r\n.overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  z-index: 999;\r\n  background: transparent;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .userButton {\r\n    padding: 0.375rem 0.75rem;\r\n    font-size: 0.8125rem;\r\n  }\r\n  \r\n  .wholesaleLabel {\r\n    font-size: 0.6875rem;\r\n  }\r\n  \r\n  .userName {\r\n    font-size: 0.8125rem;\r\n  }\r\n  \r\n  .dropdownContent {\r\n    min-width: 260px;\r\n    margin-right: 1rem;\r\n  }\r\n  \r\n  .userDetails {\r\n    padding: 1.25rem;\r\n  }\r\n  \r\n  .logoutButton {\r\n    padding: 0.875rem 1.25rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .userInfo {\r\n    display: none;\r\n  }\r\n  \r\n  .userButton {\r\n    padding: 0.5rem;\r\n    border-radius: 50%;\r\n    width: 40px;\r\n    height: 40px;\r\n    justify-content: center;\r\n  }\r\n  \r\n  .userButton::before {\r\n    content: 'W';\r\n    font-weight: 700;\r\n    font-size: 1rem;\r\n  }\r\n  \r\n  .chevron {\r\n    display: none;\r\n  }\r\n  \r\n  .dropdown {\r\n    right: -1rem;\r\n  }\r\n  \r\n  .dropdownContent {\r\n    min-width: 240px;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;AAKA;;;;;;;;;;;;;;;;AAgBA;;;;;;AAMA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;;AAOA;;;;;AAKA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;;AAUA;;;;;;;;;;;;AAWA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;;;;AAQA;;;;;;;;;;AASA;;;;;AAKA;;;;;;;;;;;;;;;;AAgBA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;;AAWA;EACE;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;;;;;EAQA;;;;;;EAMA;;;;EAIA;;;;EAIA"}}]}