/* [project]/src/app/checkout/checkout.module.css [app-client] (css) */
.checkout-module__6Nj7Kq__container {
  max-width: 1200px;
  min-height: 80vh;
  margin: 0 auto;
  padding: 8rem 1rem 2rem;
}

.checkout-module__6Nj7Kq__header {
  text-align: center;
  border-bottom: 2px solid #f3f4f6;
  margin-bottom: 3rem;
  padding-bottom: 2rem;
}

.checkout-module__6Nj7Kq__title {
  color: #1f2937;
  margin: 0 0 2rem;
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
}

.checkout-module__6Nj7Kq__stepIndicator {
  justify-content: center;
  align-items: center;
  gap: 1rem;
  display: flex;
}

.checkout-module__6Nj7Kq__step {
  flex-direction: column;
  align-items: center;
  gap: .5rem;
  display: flex;
}

.checkout-module__6Nj7Kq__step span {
  color: #6b7280;
  background: #e5e7eb;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  font-weight: 600;
  transition: all .3s;
  display: flex;
}

.checkout-module__6Nj7Kq__step.checkout-module__6Nj7Kq__active span {
  color: #fff;
  background: #2563eb;
}

.checkout-module__6Nj7Kq__step label {
  color: #4b5563;
  font-size: .9rem;
  font-weight: 600;
}

.checkout-module__6Nj7Kq__step.checkout-module__6Nj7Kq__active label {
  color: #1f2937;
}

.checkout-module__6Nj7Kq__stepConnector {
  background: #e5e7eb;
  width: 60px;
  height: 2px;
}

.checkout-module__6Nj7Kq__checkoutContent {
  grid-template-columns: 1fr 400px;
  gap: 3rem;
  display: grid;
}

.checkout-module__6Nj7Kq__mainContent {
  background: #fff;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 8px #0000001a;
}

.checkout-module__6Nj7Kq__sectionTitle {
  color: #1f2937;
  border-bottom: 1px solid #e5e7eb;
  margin: 0 0 2rem;
  padding-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 700;
}

.checkout-module__6Nj7Kq__formGrid {
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
  display: grid;
}

.checkout-module__6Nj7Kq__formGroup {
  flex-direction: column;
  display: flex;
}

.checkout-module__6Nj7Kq__formGroup.checkout-module__6Nj7Kq__fullWidth {
  grid-column: 1 / -1;
}

.checkout-module__6Nj7Kq__formGroup label {
  color: #1f2937;
  margin-bottom: .5rem;
  font-size: .9rem;
  font-weight: 600;
}

.checkout-module__6Nj7Kq__formGroup input, .checkout-module__6Nj7Kq__formGroup select {
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  padding: .75rem;
  font-size: 1rem;
  transition: border-color .2s;
}

.checkout-module__6Nj7Kq__formGroup input:focus, .checkout-module__6Nj7Kq__formGroup select:focus {
  border-color: #2563eb;
  outline: none;
}

.checkout-module__6Nj7Kq__paymentMethods {
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
  display: flex;
}

.checkout-module__6Nj7Kq__paymentMethod {
  cursor: pointer;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  transition: all .2s;
  display: flex;
}

.checkout-module__6Nj7Kq__paymentMethod:hover {
  border-color: #2563eb;
}

.checkout-module__6Nj7Kq__paymentMethod.checkout-module__6Nj7Kq__selected {
  background: #eff6ff;
  border-color: #2563eb;
}

.checkout-module__6Nj7Kq__paymentIcon {
  text-align: center;
  width: 60px;
  font-size: 2rem;
}

.checkout-module__6Nj7Kq__paymentInfo {
  flex: 1;
}

.checkout-module__6Nj7Kq__paymentInfo h3 {
  color: #1f2937;
  margin: 0 0 .25rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.checkout-module__6Nj7Kq__paymentInfo p {
  color: #4b5563;
  margin: 0;
  font-size: .9rem;
}

.checkout-module__6Nj7Kq__radioButton input {
  accent-color: #2563eb;
  width: 20px;
  height: 20px;
}

.checkout-module__6Nj7Kq__stepActions {
  border-top: 1px solid #e5e7eb;
  justify-content: flex-end;
  gap: 1rem;
  padding-top: 2rem;
  display: flex;
}

.checkout-module__6Nj7Kq__nextBtn, .checkout-module__6Nj7Kq__placeOrderBtn {
  color: #fff;
  cursor: pointer;
  background: #2563eb;
  border: none;
  border-radius: 6px;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
}

.checkout-module__6Nj7Kq__nextBtn:hover, .checkout-module__6Nj7Kq__placeOrderBtn:hover:not(:disabled) {
  background: #1d4ed8;
  transform: translateY(-2px);
}

.checkout-module__6Nj7Kq__placeOrderBtn:disabled {
  opacity: .7;
  cursor: not-allowed;
  transform: none;
}

.checkout-module__6Nj7Kq__backBtn {
  color: #4b5563;
  cursor: pointer;
  background: none;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  transition: all .3s;
}

.checkout-module__6Nj7Kq__backBtn:hover {
  color: #2563eb;
  border-color: #2563eb;
}

.checkout-module__6Nj7Kq__orderSummary {
  background: #fff;
  border-radius: 8px;
  height: fit-content;
  padding: 2rem;
  position: sticky;
  top: 2rem;
  box-shadow: 0 2px 8px #0000001a;
}

.checkout-module__6Nj7Kq__summaryTitle {
  color: #1f2937;
  border-bottom: 1px solid #e5e7eb;
  margin: 0 0 1.5rem;
  padding-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 700;
}

.checkout-module__6Nj7Kq__orderItems {
  margin-bottom: 1.5rem;
}

.checkout-module__6Nj7Kq__orderItem {
  border-bottom: 1px solid #f3f4f6;
  align-items: center;
  gap: 1rem;
  padding: 1rem 0;
  display: flex;
}

.checkout-module__6Nj7Kq__orderItem:last-child {
  border-bottom: none;
}

.checkout-module__6Nj7Kq__itemImage {
  object-fit: cover;
  border-radius: 6px;
  width: 60px;
  height: 60px;
}

.checkout-module__6Nj7Kq__itemDetails {
  flex: 1;
  min-width: 0;
}

.checkout-module__6Nj7Kq__itemDetails h4 {
  color: #1f2937;
  margin: 0 0 .25rem;
  font-size: .9rem;
  font-weight: 600;
  line-height: 1.3;
}

.checkout-module__6Nj7Kq__itemDetails p {
  color: #4b5563;
  margin: 0;
  font-size: .8rem;
}

.checkout-module__6Nj7Kq__itemPrice {
  color: #1f2937;
  font-size: .9rem;
  font-weight: 600;
}

.checkout-module__6Nj7Kq__summaryTotals {
  border-top: 1px solid #e5e7eb;
  padding-top: 1rem;
}

.checkout-module__6Nj7Kq__summaryRow {
  justify-content: space-between;
  align-items: center;
  margin-bottom: .75rem;
  display: flex;
}

.checkout-module__6Nj7Kq__summaryRow span:first-child {
  color: #4b5563;
  font-size: .9rem;
}

.checkout-module__6Nj7Kq__summaryRow span:last-child {
  color: #1f2937;
  font-size: .9rem;
  font-weight: 600;
}

.checkout-module__6Nj7Kq__totalRow {
  border-top: 2px solid #2563eb;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  padding: 1rem 0;
  display: flex;
}

.checkout-module__6Nj7Kq__totalRow span:first-child {
  color: #1f2937;
  font-size: 1.1rem;
  font-weight: 700;
}

.checkout-module__6Nj7Kq__totalRow span:last-child {
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 700;
}

@media (width <= 1024px) {
  .checkout-module__6Nj7Kq__checkoutContent {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .checkout-module__6Nj7Kq__orderSummary {
    order: -1;
    position: static;
  }
}

@media (width <= 768px) {
  .checkout-module__6Nj7Kq__container {
    padding: 1rem;
  }

  .checkout-module__6Nj7Kq__title {
    font-size: 2rem;
  }

  .checkout-module__6Nj7Kq__stepIndicator {
    gap: .5rem;
  }

  .checkout-module__6Nj7Kq__step span {
    width: 35px;
    height: 35px;
    font-size: .9rem;
  }

  .checkout-module__6Nj7Kq__stepConnector {
    width: 40px;
  }

  .checkout-module__6Nj7Kq__mainContent, .checkout-module__6Nj7Kq__orderSummary {
    padding: 1.5rem;
  }

  .checkout-module__6Nj7Kq__formGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .checkout-module__6Nj7Kq__stepActions {
    flex-direction: column-reverse;
  }

  .checkout-module__6Nj7Kq__nextBtn, .checkout-module__6Nj7Kq__placeOrderBtn, .checkout-module__6Nj7Kq__backBtn {
    justify-content: center;
    width: 100%;
  }
}

@media (width <= 480px) {
  .checkout-module__6Nj7Kq__container {
    padding: 6rem .5rem .5rem;
  }

  .checkout-module__6Nj7Kq__title {
    font-size: 1.75rem;
  }

  .checkout-module__6Nj7Kq__mainContent, .checkout-module__6Nj7Kq__orderSummary {
    padding: 1rem;
  }

  .checkout-module__6Nj7Kq__sectionTitle {
    font-size: 1.25rem;
  }

  .checkout-module__6Nj7Kq__paymentMethod {
    padding: 1rem;
  }

  .checkout-module__6Nj7Kq__paymentIcon {
    width: 40px;
    font-size: 1.5rem;
  }

  .checkout-module__6Nj7Kq__orderItem {
    text-align: center;
    flex-direction: column;
    align-items: flex-start;
  }

  .checkout-module__6Nj7Kq__itemImage {
    align-self: center;
  }
}

.checkout-module__6Nj7Kq__errorMessage {
  background-color: #fee2e2;
  border: 1px solid #fca5a5;
  border-radius: 6px;
  margin: 16px 0;
  padding: 12px 16px;
}

.checkout-module__6Nj7Kq__errorMessage p {
  color: #dc2626;
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.checkout-module__6Nj7Kq__paymentMethod {
  cursor: pointer;
  transition: all .2s;
}

.checkout-module__6Nj7Kq__paymentMethod:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px #0000001a;
}

.checkout-module__6Nj7Kq__paymentMethod.checkout-module__6Nj7Kq__selected {
  background-color: #f0f4ff;
  border-color: #1e3a8a;
}

.checkout-module__6Nj7Kq__placeOrderBtn:disabled {
  opacity: .6;
  cursor: not-allowed;
}


/*# sourceMappingURL=src_app_checkout_checkout_module_ed06fa17.css.map*/