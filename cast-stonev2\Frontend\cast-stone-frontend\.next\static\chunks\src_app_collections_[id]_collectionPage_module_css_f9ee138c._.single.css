/* [project]/src/app/collections/[id]/collectionPage.module.css [app-client] (css) */
.collectionPage-module__Lnisnq__collectionPage {
  background: #fff;
  min-height: 100vh;
}

.collectionPage-module__Lnisnq__container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 6rem 2rem 2rem;
}

.collectionPage-module__Lnisnq__loadingContainer {
  color: #1e40af;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  display: flex;
}

.collectionPage-module__Lnisnq__loadingSpinner {
  border: 3px solid #f3f3f3;
  border-top-color: #1e40af;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin-bottom: 1rem;
  animation: 1s linear infinite collectionPage-module__Lnisnq__spin;
}

@keyframes collectionPage-module__Lnisnq__spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.collectionPage-module__Lnisnq__errorContainer {
  text-align: center;
  color: #1e40af;
  padding: 4rem 2rem;
}

.collectionPage-module__Lnisnq__errorContainer h1 {
  margin-bottom: 1rem;
  font-size: 2rem;
}

.collectionPage-module__Lnisnq__collectionHeader {
  background: #f8f8f8;
  border: 2px solid #ddd;
  border-radius: 0;
  margin-bottom: 3rem;
  padding: 2rem;
}

.collectionPage-module__Lnisnq__headerContent {
  text-align: center;
}

.collectionPage-module__Lnisnq__collectionTitle {
  color: #1e40af;
  margin-bottom: 1rem;
  font-size: 3rem;
  font-weight: 700;
  line-height: 1.2;
}

.collectionPage-module__Lnisnq__collectionDescription {
  color: #666;
  max-width: 800px;
  margin-bottom: 1.5rem;
  margin-left: auto;
  margin-right: auto;
  font-size: 1.2rem;
  line-height: 1.6;
}

.collectionPage-module__Lnisnq__collectionMeta {
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
  display: flex;
}

.collectionPage-module__Lnisnq__productCount {
  color: #1e40af;
  background: #fff;
  border: 2px solid #1e40af;
  border-radius: 0;
  padding: .5rem 1rem;
  font-weight: 600;
}

.collectionPage-module__Lnisnq__collectionLevel {
  color: #666;
  background: #fff;
  border: 2px solid #ddd;
  border-radius: 0;
  padding: .5rem 1rem;
  font-weight: 600;
}

.collectionPage-module__Lnisnq__filterSection {
  background: #fff;
  border: 2px solid #ddd;
  border-radius: 0;
  margin-bottom: 2rem;
  overflow: hidden;
}

.collectionPage-module__Lnisnq__searchBar {
  background: #fafafa;
  border-bottom: 1px solid #ddd;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  display: flex;
}

.collectionPage-module__Lnisnq__searchInput {
  flex: 1;
  align-items: center;
  display: flex;
  position: relative;
}

.collectionPage-module__Lnisnq__searchIcon {
  color: #666;
  z-index: 1;
  position: absolute;
  left: 1rem;
}

.collectionPage-module__Lnisnq__searchField {
  background: #fff;
  border: 2px solid #ddd;
  border-radius: 0;
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  font-size: 1rem;
  transition: border-color .3s;
}

.collectionPage-module__Lnisnq__searchField:focus {
  border-color: #1e40af;
  outline: none;
}

.collectionPage-module__Lnisnq__filterToggle {
  color: #fff;
  cursor: pointer;
  background: #1e40af;
  border: 2px solid #1e40af;
  border-radius: 0;
  align-items: center;
  gap: .5rem;
  padding: 1rem 1.5rem;
  font-weight: 600;
  transition: all .3s;
  display: flex;
}

.collectionPage-module__Lnisnq__filterToggle:hover {
  color: #1e40af;
  background: #fff;
}

.collectionPage-module__Lnisnq__filterToggle.collectionPage-module__Lnisnq__active {
  background: #1d4ed8;
}

.collectionPage-module__Lnisnq__advancedFilters {
  background: #fff;
  border-top: 1px solid #ddd;
  padding: 2rem;
}

.collectionPage-module__Lnisnq__filterGrid {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  align-items: end;
  gap: 2rem;
  display: grid;
}

.collectionPage-module__Lnisnq__filterGroup {
  flex-direction: column;
  gap: .5rem;
  display: flex;
}

.collectionPage-module__Lnisnq__filterLabel {
  color: #1e40af;
  font-size: .9rem;
  font-weight: 600;
}

.collectionPage-module__Lnisnq__priceRange {
  align-items: center;
  gap: .5rem;
  display: flex;
}

.collectionPage-module__Lnisnq__priceInput {
  border: 2px solid #ddd;
  border-radius: 0;
  flex: 1;
  padding: .75rem;
  font-size: .9rem;
}

.collectionPage-module__Lnisnq__priceInput:focus {
  border-color: #1e40af;
  outline: none;
}

.collectionPage-module__Lnisnq__checkboxLabel {
  color: #1e40af;
  cursor: pointer;
  align-items: center;
  gap: .5rem;
  font-weight: 600;
  display: flex;
}

.collectionPage-module__Lnisnq__checkbox {
  accent-color: #1e40af;
  width: 18px;
  height: 18px;
}

.collectionPage-module__Lnisnq__sortControls {
  gap: .5rem;
  display: flex;
}

.collectionPage-module__Lnisnq__sortSelect {
  background: #fff;
  border: 2px solid #ddd;
  border-radius: 0;
  flex: 1;
  padding: .75rem;
  font-size: .9rem;
}

.collectionPage-module__Lnisnq__sortSelect:focus {
  border-color: #1e40af;
  outline: none;
}

.collectionPage-module__Lnisnq__sortDirection {
  color: #fff;
  cursor: pointer;
  background: #1e40af;
  border: 2px solid #1e40af;
  border-radius: 0;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  transition: all .3s;
  display: flex;
}

.collectionPage-module__Lnisnq__sortDirection:hover {
  color: #1e40af;
  background: #fff;
}

.collectionPage-module__Lnisnq__sortDirection.collectionPage-module__Lnisnq__desc {
  transform: rotate(180deg);
}

.collectionPage-module__Lnisnq__clearFilters {
  color: #fff;
  cursor: pointer;
  background: #dc3545;
  border: 2px solid #dc3545;
  border-radius: 0;
  width: 100%;
  padding: .75rem 1rem;
  font-weight: 600;
  transition: all .3s;
}

.collectionPage-module__Lnisnq__clearFilters:hover {
  color: #dc3545;
  background: #fff;
}

.collectionPage-module__Lnisnq__productsSection {
  margin-top: 2rem;
}

@media (width <= 1024px) {
  .collectionPage-module__Lnisnq__collectionTitle {
    font-size: 2.5rem;
  }

  .collectionPage-module__Lnisnq__filterGrid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1.5rem;
  }
}

@media (width <= 768px) {
  .collectionPage-module__Lnisnq__container {
    padding: 1rem;
  }

  .collectionPage-module__Lnisnq__collectionHeader {
    margin-bottom: 2rem;
    padding: 1.5rem;
  }

  .collectionPage-module__Lnisnq__collectionTitle {
    font-size: 2rem;
  }

  .collectionPage-module__Lnisnq__collectionDescription {
    font-size: 1rem;
  }

  .collectionPage-module__Lnisnq__collectionMeta {
    flex-direction: column;
    gap: 1rem;
  }

  .collectionPage-module__Lnisnq__searchBar {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .collectionPage-module__Lnisnq__searchInput {
    width: 100%;
  }

  .collectionPage-module__Lnisnq__filterToggle {
    justify-content: center;
    width: 100%;
  }

  .collectionPage-module__Lnisnq__filterGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .collectionPage-module__Lnisnq__priceRange {
    flex-direction: column;
    align-items: stretch;
  }

  .collectionPage-module__Lnisnq__sortControls {
    flex-direction: column;
  }
}

@media (width <= 480px) {
  .collectionPage-module__Lnisnq__collectionTitle {
    font-size: 1.75rem;
  }

  .collectionPage-module__Lnisnq__advancedFilters {
    padding: 1rem;
  }

  .collectionPage-module__Lnisnq__searchField {
    padding: .75rem .75rem .75rem 2.5rem;
  }

  .collectionPage-module__Lnisnq__filterToggle {
    padding: .75rem 1rem;
  }
}

/*# sourceMappingURL=src_app_collections_%5Bid%5D_collectionPage_module_css_f9ee138c._.single.css.map*/