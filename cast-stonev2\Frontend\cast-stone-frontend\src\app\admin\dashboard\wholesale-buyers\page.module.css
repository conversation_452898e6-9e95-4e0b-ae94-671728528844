.container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  margin-bottom: 2rem;
}

.header h1 {
  margin: 0 0 0.5rem;
  color: #1f2937;
  font-size: 2rem;
  font-weight: 700;
}

.header p {
  margin: 0;
  color: #6b7280;
  font-size: 1rem;
}

.errorBanner {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 1rem;
  margin-bottom: 1.5rem;
  border-radius: 6px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.errorBanner p {
  margin: 0;
  font-weight: 500;
}

.closeError {
  background: none;
  border: none;
  color: #dc2626;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  text-align: center;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.statCard {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.statCard h3 {
  margin: 0 0 0.5rem;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statNumber {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
}

.statNumber.pending {
  color: #f59e0b;
}

.statNumber.approved {
  color: #10b981;
}

.statNumber.rejected {
  color: #ef4444;
}

.filters {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.filterGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filterGroup label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.searchInput,
.statusFilter {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  min-width: 200px;
}

.searchInput:focus,
.statusFilter:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.tableContainer {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th {
  background: #f9fafb;
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  border-bottom: 1px solid #e5e7eb;
}

.table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e5e7eb;
  color: #374151;
  font-size: 0.875rem;
}

.table tbody tr:hover {
  background: #f9fafb;
}

.statusBadge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statusPending {
  background: #fef3c7;
  color: #92400e;
}

.statusApproved {
  background: #d1fae5;
  color: #065f46;
}

.statusRejected {
  background: #fee2e2;
  color: #991b1b;
}

.viewButton {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.375rem 0.75rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.viewButton:hover {
  background: #2563eb;
}

.emptyState {
  padding: 3rem;
  text-align: center;
  color: #6b7280;
}

/* Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal {
  background: white;
  border-radius: 12px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e5e7eb;
}

.modalHeader h2 {
  margin: 0;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.closeButton:hover {
  background: #f3f4f6;
}

.modalContent {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

.buyerInfo h3 {
  margin: 2rem 0 1rem 0;
  color: #1f2937;
  font-size: 1.125rem;
  font-weight: 600;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.buyerInfo h3:first-child {
  margin-top: 0;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.infoGrid div {
  color: #374151;
  font-size: 0.875rem;
  line-height: 1.5;
}

.address {
  color: #374151;
  font-size: 0.875rem;
  line-height: 1.5;
}

.hearAbout {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.tag {
  background: #e0e7ff;
  color: #3730a3;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.comments {
  background: #f9fafb;
  padding: 1rem;
  border-radius: 6px;
  color: #374151;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
}

.statusInfo {
  background: #f9fafb;
  padding: 1rem;
  border-radius: 6px;
}

.statusInfo p {
  margin: 0 0 0.5rem 0;
  color: #374151;
  font-size: 0.875rem;
}

.statusInfo p:last-child {
  margin-bottom: 0;
}

.modalActions {
  padding: 1.5rem 2rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  gap: 1rem;
}

.approveButton {
  background: #10b981;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.approveButton:hover:not(:disabled) {
  background: #059669;
}

.rejectButton {
  background: #ef4444;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.rejectButton:hover:not(:disabled) {
  background: #dc2626;
}

.rejectForm {
  flex: 1;
}

.rejectTextarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  resize: vertical;
  margin-bottom: 1rem;
}

.rejectTextarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.rejectActions {
  display: flex;
  gap: 1rem;
}

.confirmRejectButton {
  background: #ef4444;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.confirmRejectButton:hover:not(:disabled) {
  background: #dc2626;
}

.confirmRejectButton:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.cancelButton {
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.approveButton:disabled,
.rejectButton:disabled,
.cancelButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .filters {
    flex-direction: column;
  }
  
  .searchInput,
  .statusFilter {
    min-width: auto;
  }
  
  .table {
    font-size: 0.75rem;
  }
  
  .table th,
  .table td {
    padding: 0.5rem;
  }
  
  .modalOverlay {
    padding: 0.5rem;
  }
  
  .modalContent {
    padding: 1rem;
  }
  
  .modalActions {
    flex-direction: column;
    padding: 1rem;
  }
  
  .rejectActions {
    flex-direction: column;
  }
}
