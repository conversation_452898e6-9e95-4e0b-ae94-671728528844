/* [project]/src/components/wholesale/WholesaleSignupForm.module.css [app-client] (css) */
.WholesaleSignupForm-module__HafJYa__formContainer {
  background: #fff;
  border-radius: 12px;
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  box-shadow: 0 4px 6px #0000001a;
}

.WholesaleSignupForm-module__HafJYa__progressBar {
  margin-bottom: 3rem;
  padding: 0 2rem;
  position: relative;
}

.WholesaleSignupForm-module__HafJYa__progressSteps {
  z-index: 2;
  justify-content: space-between;
  display: flex;
  position: relative;
}

.WholesaleSignupForm-module__HafJYa__progressStep {
  flex-direction: column;
  align-items: center;
  gap: .5rem;
  display: flex;
}

.WholesaleSignupForm-module__HafJYa__stepNumber {
  color: #6b7280;
  background: #e5e7eb;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  font-weight: 600;
  transition: all .3s;
  display: flex;
}

.WholesaleSignupForm-module__HafJYa__progressStep.WholesaleSignupForm-module__HafJYa__active .WholesaleSignupForm-module__HafJYa__stepNumber {
  color: #fff;
  background: #3b82f6;
}

.WholesaleSignupForm-module__HafJYa__progressStep.WholesaleSignupForm-module__HafJYa__completed .WholesaleSignupForm-module__HafJYa__stepNumber {
  color: #fff;
  background: #10b981;
}

.WholesaleSignupForm-module__HafJYa__stepLabel {
  color: #6b7280;
  font-size: .875rem;
  font-weight: 500;
}

.WholesaleSignupForm-module__HafJYa__progressStep.WholesaleSignupForm-module__HafJYa__active .WholesaleSignupForm-module__HafJYa__stepLabel, .WholesaleSignupForm-module__HafJYa__progressStep.WholesaleSignupForm-module__HafJYa__completed .WholesaleSignupForm-module__HafJYa__stepLabel {
  color: #374151;
}

.WholesaleSignupForm-module__HafJYa__progressLine {
  z-index: 1;
  background: #10b981;
  height: 2px;
  transition: width .3s;
  position: absolute;
  top: 20px;
  left: 2rem;
  right: 2rem;
}

.WholesaleSignupForm-module__HafJYa__progressLine:before {
  content: "";
  z-index: -1;
  background: #e5e7eb;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.WholesaleSignupForm-module__HafJYa__stepContent {
  margin-bottom: 2rem;
}

.WholesaleSignupForm-module__HafJYa__stepContent h3 {
  color: #1f2937;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.WholesaleSignupForm-module__HafJYa__formGroup {
  margin-bottom: 1.5rem;
}

.WholesaleSignupForm-module__HafJYa__formGroup label {
  color: #374151;
  margin-bottom: .5rem;
  font-size: .875rem;
  font-weight: 500;
  display: block;
}

.WholesaleSignupForm-module__HafJYa__formGroup input, .WholesaleSignupForm-module__HafJYa__formGroup select, .WholesaleSignupForm-module__HafJYa__formGroup textarea {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  width: 100%;
  padding: .75rem;
  font-size: 1rem;
  transition: border-color .2s, box-shadow .2s;
}

.WholesaleSignupForm-module__HafJYa__formGroup input:focus, .WholesaleSignupForm-module__HafJYa__formGroup select:focus, .WholesaleSignupForm-module__HafJYa__formGroup textarea:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 3px #3b82f61a;
}

.WholesaleSignupForm-module__HafJYa__formGroup input.WholesaleSignupForm-module__HafJYa__error, .WholesaleSignupForm-module__HafJYa__formGroup select.WholesaleSignupForm-module__HafJYa__error, .WholesaleSignupForm-module__HafJYa__formGroup textarea.WholesaleSignupForm-module__HafJYa__error {
  border-color: #ef4444;
}

.WholesaleSignupForm-module__HafJYa__formGroup input.WholesaleSignupForm-module__HafJYa__error:focus, .WholesaleSignupForm-module__HafJYa__formGroup select.WholesaleSignupForm-module__HafJYa__error:focus, .WholesaleSignupForm-module__HafJYa__formGroup textarea.WholesaleSignupForm-module__HafJYa__error:focus {
  box-shadow: 0 0 0 3px #ef44441a;
}

.WholesaleSignupForm-module__HafJYa__formRow {
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  display: grid;
}

.WholesaleSignupForm-module__HafJYa__formRow.WholesaleSignupForm-module__HafJYa__triple {
  grid-template-columns: 1fr 1fr 1fr;
}

.WholesaleSignupForm-module__HafJYa__errorText {
  color: #ef4444;
  margin-top: .25rem;
  font-size: .875rem;
  display: block;
}

.WholesaleSignupForm-module__HafJYa__checkboxGroup {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: .75rem;
  margin-top: .5rem;
  display: grid;
}

.WholesaleSignupForm-module__HafJYa__checkboxLabel {
  cursor: pointer;
  color: #374151;
  align-items: center;
  gap: .5rem;
  font-size: .875rem;
  display: flex;
}

.WholesaleSignupForm-module__HafJYa__checkboxLabel input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.WholesaleSignupForm-module__HafJYa__formActions {
  border-top: 1px solid #e5e7eb;
  justify-content: space-between;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 2rem;
  display: flex;
}

.WholesaleSignupForm-module__HafJYa__primaryButton {
  color: #fff;
  cursor: pointer;
  background: #3b82f6;
  border: none;
  border-radius: 6px;
  min-width: 120px;
  padding: .75rem 2rem;
  font-weight: 600;
  transition: background-color .2s;
}

.WholesaleSignupForm-module__HafJYa__primaryButton:hover:not(:disabled) {
  background: #2563eb;
}

.WholesaleSignupForm-module__HafJYa__primaryButton:disabled {
  cursor: not-allowed;
  background: #9ca3af;
}

.WholesaleSignupForm-module__HafJYa__secondaryButton {
  color: #374151;
  cursor: pointer;
  background: #fff;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  min-width: 120px;
  padding: .75rem 2rem;
  font-weight: 600;
  transition: all .2s;
}

.WholesaleSignupForm-module__HafJYa__secondaryButton:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.WholesaleSignupForm-module__HafJYa__secondaryButton:disabled {
  color: #9ca3af;
  cursor: not-allowed;
  background: #f9fafb;
}

@media (width <= 768px) {
  .WholesaleSignupForm-module__HafJYa__formContainer {
    margin: 1rem;
    padding: 1.5rem;
  }

  .WholesaleSignupForm-module__HafJYa__progressBar {
    padding: 0 1rem;
  }

  .WholesaleSignupForm-module__HafJYa__formRow, .WholesaleSignupForm-module__HafJYa__formRow.WholesaleSignupForm-module__HafJYa__triple, .WholesaleSignupForm-module__HafJYa__checkboxGroup {
    grid-template-columns: 1fr;
  }

  .WholesaleSignupForm-module__HafJYa__formActions {
    flex-direction: column;
  }

  .WholesaleSignupForm-module__HafJYa__stepLabel {
    display: none;
  }
}

@media (width <= 480px) {
  .WholesaleSignupForm-module__HafJYa__formContainer {
    margin: .5rem;
    padding: 1rem;
  }

  .WholesaleSignupForm-module__HafJYa__progressSteps {
    justify-content: center;
    gap: 2rem;
  }

  .WholesaleSignupForm-module__HafJYa__stepNumber {
    width: 32px;
    height: 32px;
    font-size: .875rem;
  }
}


/* [project]/src/components/wholesale/WholesaleLogin.module.css [app-client] (css) */
.WholesaleLogin-module__KgOrfG__loginContainer {
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 2rem;
  display: flex;
}

.WholesaleLogin-module__KgOrfG__loginCard {
  background: #fff;
  border-radius: 12px;
  width: 100%;
  max-width: 400px;
  overflow: hidden;
  box-shadow: 0 4px 6px #0000001a;
}

.WholesaleLogin-module__KgOrfG__loginHeader {
  text-align: center;
  color: #fff;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  padding: 2rem 2rem 1rem;
}

.WholesaleLogin-module__KgOrfG__loginHeader h2 {
  margin: 0 0 .5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.WholesaleLogin-module__KgOrfG__loginHeader p {
  opacity: .9;
  margin: 0;
  font-size: .875rem;
}

.WholesaleLogin-module__KgOrfG__loginForm {
  padding: 2rem;
}

.WholesaleLogin-module__KgOrfG__formGroup {
  margin-bottom: 1.5rem;
}

.WholesaleLogin-module__KgOrfG__formGroup label {
  color: #374151;
  margin-bottom: .5rem;
  font-size: .875rem;
  font-weight: 500;
  display: block;
}

.WholesaleLogin-module__KgOrfG__formGroup input {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  width: 100%;
  padding: .75rem;
  font-size: 1rem;
  transition: border-color .2s, box-shadow .2s;
}

.WholesaleLogin-module__KgOrfG__formGroup input:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 3px #3b82f61a;
}

.WholesaleLogin-module__KgOrfG__formGroup input.WholesaleLogin-module__KgOrfG__error {
  border-color: #ef4444;
}

.WholesaleLogin-module__KgOrfG__formGroup input.WholesaleLogin-module__KgOrfG__error:focus {
  box-shadow: 0 0 0 3px #ef44441a;
}

.WholesaleLogin-module__KgOrfG__formGroup input:disabled {
  cursor: not-allowed;
  background-color: #f9fafb;
}

.WholesaleLogin-module__KgOrfG__errorText {
  color: #ef4444;
  margin-top: .25rem;
  font-size: .875rem;
  display: block;
}

.WholesaleLogin-module__KgOrfG__loginButton {
  color: #fff;
  cursor: pointer;
  background: #3b82f6;
  border: none;
  border-radius: 6px;
  width: 100%;
  margin-top: .5rem;
  padding: .75rem;
  font-size: 1rem;
  font-weight: 600;
  transition: background-color .2s;
}

.WholesaleLogin-module__KgOrfG__loginButton:hover:not(:disabled) {
  background: #2563eb;
}

.WholesaleLogin-module__KgOrfG__loginButton:disabled {
  cursor: not-allowed;
  background: #9ca3af;
}

.WholesaleLogin-module__KgOrfG__loginFooter {
  text-align: center;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  padding: 1.5rem 2rem 2rem;
}

.WholesaleLogin-module__KgOrfG__loginFooter p {
  color: #6b7280;
  margin: 0;
  font-size: .875rem;
}

.WholesaleLogin-module__KgOrfG__linkButton {
  color: #3b82f6;
  cursor: pointer;
  font-size: inherit;
  background: none;
  border: none;
  padding: 0;
  text-decoration: underline;
  transition: color .2s;
}

.WholesaleLogin-module__KgOrfG__linkButton:hover:not(:disabled) {
  color: #2563eb;
}

.WholesaleLogin-module__KgOrfG__linkButton:disabled {
  color: #9ca3af;
  cursor: not-allowed;
}

@media (width <= 480px) {
  .WholesaleLogin-module__KgOrfG__loginContainer {
    padding: 1rem;
  }

  .WholesaleLogin-module__KgOrfG__loginCard {
    max-width: none;
  }

  .WholesaleLogin-module__KgOrfG__loginHeader {
    padding: 1.5rem 1.5rem 1rem;
  }

  .WholesaleLogin-module__KgOrfG__loginForm {
    padding: 1.5rem;
  }

  .WholesaleLogin-module__KgOrfG__loginFooter {
    padding: 1rem 1.5rem 1.5rem;
  }
}


/* [project]/src/app/wholesale-signup/page.module.css [app-client] (css) */
.page-module__7gVXna__pageContainer {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  margin-top: 4.2rem;
}

.page-module__7gVXna__header {
  color: #fff;
  text-align: center;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  padding: 3rem 0;
}

.page-module__7gVXna__headerContent h1 {
  margin: 0 0 1rem;
  font-size: 2.5rem;
  font-weight: 700;
}

.page-module__7gVXna__headerContent p {
  opacity: .9;
  margin: 0;
  font-size: 1.125rem;
}

.page-module__7gVXna__errorBanner {
  color: #dc2626;
  background: #fef2f2;
  border: 1px solid #fecaca;
  justify-content: space-between;
  align-items: center;
  margin: 0;
  padding: 1rem;
  display: flex;
}

.page-module__7gVXna__errorBanner p {
  margin: 0;
  font-weight: 500;
}

.page-module__7gVXna__closeError {
  color: #dc2626;
  cursor: pointer;
  background: none;
  border: none;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  padding: 0;
  font-size: 1.5rem;
  display: flex;
}

.page-module__7gVXna__contentContainer {
  grid-template-columns: 1fr 300px;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  display: grid;
}

.page-module__7gVXna__mainContent {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px #0000001a;
}

.page-module__7gVXna__sidebar {
  background: #fff;
  border-radius: 12px;
  height: fit-content;
  padding: 2rem;
  position: sticky;
  top: 2rem;
  box-shadow: 0 4px 6px #0000001a;
}

.page-module__7gVXna__benefits h3 {
  color: #1f2937;
  margin: 0 0 1.5rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.page-module__7gVXna__benefits ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.page-module__7gVXna__benefits li {
  color: #374151;
  margin-bottom: .75rem;
  padding-left: 1.5rem;
  font-size: .875rem;
  line-height: 1.5;
  position: relative;
}

.page-module__7gVXna__benefits li:before {
  content: "✓";
  color: #10b981;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.page-module__7gVXna__formHeader {
  border-bottom: 1px solid #e5e7eb;
  padding: 2rem 2rem 1rem;
}

.page-module__7gVXna__formHeader h2 {
  color: #1f2937;
  margin: 0 0 .5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.page-module__7gVXna__formHeader p {
  color: #6b7280;
  margin: 0 0 1rem;
  font-size: .875rem;
}

.page-module__7gVXna__backToLogin {
  color: #3b82f6;
  cursor: pointer;
  background: none;
  border: none;
  padding: 0;
  font-size: .875rem;
  text-decoration: underline;
}

.page-module__7gVXna__backToLogin:hover {
  color: #2563eb;
}

.page-module__7gVXna__messageContainer {
  max-width: 600px;
  margin: 2rem auto;
  padding: 0 2rem;
}

.page-module__7gVXna__successMessage, .page-module__7gVXna__pendingMessage {
  text-align: center;
  background: #fff;
  border-radius: 12px;
  padding: 3rem;
  box-shadow: 0 4px 6px #0000001a;
}

.page-module__7gVXna__successIcon, .page-module__7gVXna__pendingIcon {
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  font-size: 2rem;
  display: flex;
}

.page-module__7gVXna__successIcon {
  color: #10b981;
  background: #d1fae5;
}

.page-module__7gVXna__pendingIcon {
  color: #f59e0b;
  background: #fef3c7;
}

.page-module__7gVXna__successMessage h2, .page-module__7gVXna__pendingMessage h2 {
  color: #1f2937;
  margin: 0 0 1rem;
  font-size: 1.75rem;
  font-weight: 600;
}

.page-module__7gVXna__successMessage p, .page-module__7gVXna__pendingMessage p {
  color: #6b7280;
  margin: 0 0 2rem;
  font-size: 1rem;
  line-height: 1.6;
}

.page-module__7gVXna__nextSteps {
  text-align: left;
  background: #f9fafb;
  border-radius: 8px;
  margin: 2rem 0;
  padding: 1.5rem;
}

.page-module__7gVXna__nextSteps h3 {
  color: #1f2937;
  margin: 0 0 1rem;
  font-size: 1.125rem;
  font-weight: 600;
}

.page-module__7gVXna__nextSteps ol {
  color: #374151;
  margin: 0;
  padding-left: 1.5rem;
}

.page-module__7gVXna__nextSteps li {
  margin-bottom: .5rem;
  line-height: 1.5;
}

.page-module__7gVXna__contactInfo {
  background: #f0f9ff;
  border-radius: 8px;
  margin: 2rem 0;
  padding: 1.5rem;
}

.page-module__7gVXna__contactInfo p {
  color: #374151;
  margin: 0;
  font-size: .875rem;
}

.page-module__7gVXna__contactInfo a {
  color: #3b82f6;
  text-decoration: none;
}

.page-module__7gVXna__contactInfo a:hover {
  text-decoration: underline;
}

.page-module__7gVXna__primaryButton {
  color: #fff;
  cursor: pointer;
  background: #3b82f6;
  border: none;
  border-radius: 6px;
  padding: .75rem 2rem;
  font-weight: 600;
  transition: background-color .2s;
}

.page-module__7gVXna__primaryButton:hover {
  background: #2563eb;
}

@media (width <= 1024px) {
  .page-module__7gVXna__contentContainer {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .page-module__7gVXna__sidebar {
    order: -1;
    position: static;
  }
}

@media (width <= 768px) {
  .page-module__7gVXna__header {
    padding: 2rem 1rem;
  }

  .page-module__7gVXna__headerContent h1 {
    font-size: 2rem;
  }

  .page-module__7gVXna__contentContainer {
    padding: 1rem;
  }

  .page-module__7gVXna__sidebar {
    padding: 1.5rem;
  }

  .page-module__7gVXna__formHeader {
    padding: 1.5rem 1.5rem 1rem;
  }

  .page-module__7gVXna__successMessage, .page-module__7gVXna__pendingMessage {
    padding: 2rem;
  }

  .page-module__7gVXna__messageContainer {
    padding: 0 1rem;
  }
}

@media (width <= 480px) {
  .page-module__7gVXna__headerContent h1 {
    font-size: 1.75rem;
  }

  .page-module__7gVXna__headerContent p {
    font-size: 1rem;
  }

  .page-module__7gVXna__successMessage, .page-module__7gVXna__pendingMessage {
    padding: 1.5rem;
  }

  .page-module__7gVXna__successIcon, .page-module__7gVXna__pendingIcon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
}


/*# sourceMappingURL=src_8fcd814b._.css.map*/