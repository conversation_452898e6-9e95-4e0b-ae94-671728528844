{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/cart/CartSummary/cartSummary.module.css"], "sourcesContent": ["/* Cart Summary Styles - Magazine/Editorial Theme */\r\n.cartSummary {\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  padding: 2rem;\r\n  position: sticky;\r\n  top: 2rem;\r\n  height: fit-content;\r\n}\r\n\r\n.title {\r\n  color: #1f2937;\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  margin: 0 0 1.5rem 0;\r\n  text-align: center;\r\n  border-bottom: 2px solid #f3f4f6;\r\n  padding-bottom: 1rem;\r\n}\r\n\r\n/* Summary Details */\r\n.summaryDetails {\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.summaryRow {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 0.75rem;\r\n}\r\n\r\n.label {\r\n  color: #4b5563;\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.value {\r\n  color: #1f2937;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.freeShipping {\r\n  color: #059669;\r\n  font-size: 0.85rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.divider {\r\n  height: 1px;\r\n  background: #e5e7eb;\r\n  margin: 1rem 0;\r\n}\r\n\r\n.totalRow {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 1rem 0;\r\n  border-top: 2px solid #2563eb;\r\n  margin-top: 1rem;\r\n}\r\n\r\n.totalLabel {\r\n  color: #1f2937;\r\n  font-size: 1.25rem;\r\n  font-weight: 700;\r\n}\r\n\r\n.totalValue {\r\n  color: #1f2937;\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n}\r\n\r\n/* Shipping Notice */\r\n.shippingNotice {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  background: #f0f9ff;\r\n  border: 1px solid #bae6fd;\r\n  border-radius: 6px;\r\n  padding: 0.75rem;\r\n  margin-bottom: 1.5rem;\r\n  color: #0369a1;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.infoIcon {\r\n  width: 16px;\r\n  height: 16px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* Action Buttons */\r\n.actionButtons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.75rem;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.checkoutBtn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.5rem;\r\n  padding: 1rem 1.5rem;\r\n  background: #2563eb;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 6px;\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  text-decoration: none;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.checkoutBtn:hover {\r\n  background: #1d4ed8;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);\r\n}\r\n\r\n.checkoutIcon {\r\n  width: 20px;\r\n  height: 20px;\r\n  stroke-width: 2;\r\n}\r\n\r\n.clearBtn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.5rem;\r\n  padding: 0.75rem 1rem;\r\n  background: transparent;\r\n  color: #dc2626;\r\n  border: 2px solid #dc2626;\r\n  border-radius: 6px;\r\n  font-weight: 600;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.clearBtn:hover:not(:disabled) {\r\n  background: #dc2626;\r\n  color: white;\r\n}\r\n\r\n.clearBtn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.clearIcon {\r\n  width: 18px;\r\n  height: 18px;\r\n  stroke-width: 2;\r\n}\r\n\r\n/* Security Notice */\r\n.securityNotice {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.5rem;\r\n  color: #059669;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  text-align: center;\r\n}\r\n\r\n.securityIcon {\r\n  width: 16px;\r\n  height: 16px;\r\n  stroke-width: 2;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .cartSummary {\r\n    position: static;\r\n    margin-top: 2rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .cartSummary {\r\n    padding: 1.5rem;\r\n    margin-top: 1.5rem;\r\n  }\r\n\r\n  .title {\r\n    font-size: 1.25rem;\r\n    margin-bottom: 1rem;\r\n  }\r\n\r\n  .totalLabel {\r\n    font-size: 1.1rem;\r\n  }\r\n\r\n  .totalValue {\r\n    font-size: 1.25rem;\r\n  }\r\n\r\n  .checkoutBtn {\r\n    padding: 0.875rem 1.25rem;\r\n    font-size: 0.95rem;\r\n  }\r\n\r\n  .actionButtons {\r\n    gap: 0.5rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .cartSummary {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .summaryRow {\r\n    margin-bottom: 0.5rem;\r\n  }\r\n\r\n  .label,\r\n  .value {\r\n    font-size: 0.9rem;\r\n  }\r\n\r\n  .totalRow {\r\n    padding: 0.75rem 0;\r\n  }\r\n\r\n  .checkoutBtn {\r\n    padding: 0.75rem 1rem;\r\n    font-size: 0.9rem;\r\n  }\r\n\r\n  .clearBtn {\r\n    padding: 0.625rem 0.875rem;\r\n    font-size: 0.85rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;;;;AAUA;;;;;;;;;;AAWA;;;;AAIA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;AASA;;;;;;AAMA;;;;;;AAOA;;;;;;;;;;;;;AAaA;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;;;;;;;AAiBA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;;;;;AAgBA;;;;;AAKA;;;;;AAKA;;;;;;AAOA;;;;;;;;;;;AAWA;;;;;;AAOA;EACE;;;;;;AAMF;EACE;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAKA;;;;EAIA;;;;;EAKA"}}]}