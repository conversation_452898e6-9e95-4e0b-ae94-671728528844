{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/ProductCard/productCard.module.css"], "sourcesContent": ["/* Product Card Styles - Magazine/Editorial Theme */\r\n.productCard {\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.productCard:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n/* Image Container */\r\n.imageContainer {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 250px;\r\n  overflow: hidden;\r\n}\r\n\r\n.productImage {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.productCard:hover .productImage {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.outOfStockOverlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.7);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-weight: 600;\r\n  font-size: 1.1rem;\r\n}\r\n\r\n/* Product Info */\r\n.productInfo {\r\n  padding: 1.5rem;\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.productName {\r\n  color: #1f2937;\r\n  font-size: 1.25rem;\r\n  font-weight: 700;\r\n  margin: 0 0 0.75rem 0;\r\n  line-height: 1.3;\r\n}\r\n\r\n.productDescription {\r\n  color: #4b5563;\r\n  font-size: 0.9rem;\r\n  line-height: 1.5;\r\n  margin: 0 0 1rem 0;\r\n  flex: 1;\r\n}\r\n\r\n/* Price Container */\r\n.priceContainer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 0.75rem;\r\n}\r\n\r\n.priceSection {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.25rem;\r\n}\r\n\r\n.price {\r\n  color: #1f2937;\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n}\r\n\r\n.wholesaleLabel {\r\n  color: #059669;\r\n  font-size: 0.75rem;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.retailPrice {\r\n  color: #6b7280;\r\n  font-size: 0.875rem;\r\n  text-decoration: line-through;\r\n}\r\n\r\n.collection {\r\n  color: #1e40af;\r\n  font-size: 0.85rem;\r\n  font-style: italic;\r\n  align-self: flex-end;\r\n}\r\n\r\n/* Stock Info */\r\n.stockInfo {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.inStock {\r\n  color: #059669;\r\n  font-size: 0.85rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.outOfStock {\r\n  color: #dc2626;\r\n  font-size: 0.85rem;\r\n  font-weight: 600;\r\n}\r\n\r\n/* Action Buttons */\r\n.actionButtons {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.75rem;\r\n  margin-top: auto;\r\n}\r\n\r\n.viewDetailsBtn {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 0.75rem 1rem;\r\n  background: transparent;\r\n  color: #1e40af;\r\n  border: 2px solid #1e40af;\r\n  border-radius: 4px;\r\n  text-decoration: none;\r\n  font-weight: 600;\r\n  font-size: 0.9rem;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.viewDetailsBtn:hover {\r\n  background: #1e40af;\r\n  color: white;\r\n}\r\n\r\n/* Add to Cart Section */\r\n.addToCartSection {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.quantitySelector {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.5rem;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.quantityBtn {\r\n  width: 32px;\r\n  height: 32px;\r\n  border: 1px solid #d1d5db;\r\n  background: white;\r\n  color: #1f2937;\r\n  border-radius: 4px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.quantityBtn:hover:not(:disabled) {\r\n  background: #f3f4f6;\r\n  border-color: #1e40af;\r\n}\r\n\r\n.quantityBtn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.quantity {\r\n  min-width: 40px;\r\n  text-align: center;\r\n  font-weight: 600;\r\n  color: #1f2937;\r\n}\r\n\r\n.addToCartBtn {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.5rem;\r\n  padding: 0.875rem 1rem;\r\n  background: #1e40af;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 4px;\r\n  font-weight: 600;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.addToCartBtn:hover:not(:disabled) {\r\n  background: #1d4ed8;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.addToCartBtn:disabled {\r\n  opacity: 0.7;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.cartIcon {\r\n  width: 18px;\r\n  height: 18px;\r\n  stroke-width: 2;\r\n}\r\n\r\n.loading {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.loading::after {\r\n  content: '';\r\n  width: 16px;\r\n  height: 16px;\r\n  border: 2px solid transparent;\r\n  border-top: 2px solid currentColor;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .productCard {\r\n    margin-bottom: 1rem;\r\n  }\r\n  \r\n  .imageContainer {\r\n    height: 200px;\r\n  }\r\n  \r\n  .productInfo {\r\n    padding: 1rem;\r\n  }\r\n  \r\n  .productName {\r\n    font-size: 1.1rem;\r\n  }\r\n  \r\n  .price {\r\n    font-size: 1.25rem;\r\n  }\r\n  \r\n  .actionButtons {\r\n    gap: 0.5rem;\r\n  }\r\n  \r\n  .addToCartSection {\r\n    flex-direction: column;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;;;;;AAWA;;;;;AAMA;;;;;;;AAOA;;;;;;;AAOA;;;;AAIA;;;;;;;;;;;;AAgBA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;AASA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;;;AAQA;;;;;;AAMA;;;;;;;AAQA;;;;AAIA;;;;;;AAMA;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;;;;;AAeA;;;;;AAMA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;;;;;;;;;AAeA;;;;;AAKA;;;;;AAKA;;;;;;;AAOA;;;;;;;;;;;;;;;;AAgBA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;AAOA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA", "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/ProductGrid/productGrid.module.css"], "sourcesContent": ["/* Product Grid Styles - Magazine/Editorial Theme */\r\n.productGrid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\r\n  gap: 2rem;\r\n  padding: 2rem 0;\r\n}\r\n\r\n/* Loading States */\r\n.loadingContainer {\r\n  padding: 2rem 0;\r\n}\r\n\r\n.loadingGrid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\r\n  gap: 2rem;\r\n}\r\n\r\n.loadingCard {\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n  animation: pulse 1.5s ease-in-out infinite;\r\n}\r\n\r\n.loadingImage {\r\n  width: 100%;\r\n  height: 250px;\r\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\r\n  background-size: 200% 100%;\r\n  animation: shimmer 1.5s infinite;\r\n}\r\n\r\n.loadingContent {\r\n  padding: 1.5rem;\r\n}\r\n\r\n.loadingTitle {\r\n  height: 1.5rem;\r\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\r\n  background-size: 200% 100%;\r\n  animation: shimmer 1.5s infinite;\r\n  border-radius: 4px;\r\n  margin-bottom: 0.75rem;\r\n}\r\n\r\n.loadingDescription {\r\n  height: 1rem;\r\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\r\n  background-size: 200% 100%;\r\n  animation: shimmer 1.5s infinite;\r\n  border-radius: 4px;\r\n  margin-bottom: 0.5rem;\r\n  width: 80%;\r\n}\r\n\r\n.loadingPrice {\r\n  height: 1.25rem;\r\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\r\n  background-size: 200% 100%;\r\n  animation: shimmer 1.5s infinite;\r\n  border-radius: 4px;\r\n  margin-bottom: 1rem;\r\n  width: 60%;\r\n}\r\n\r\n.loadingButton {\r\n  height: 2.5rem;\r\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\r\n  background-size: 200% 100%;\r\n  animation: shimmer 1.5s infinite;\r\n  border-radius: 4px;\r\n}\r\n\r\n@keyframes shimmer {\r\n  0% {\r\n    background-position: -200% 0;\r\n  }\r\n  100% {\r\n    background-position: 200% 0;\r\n  }\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% {\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    opacity: 0.8;\r\n  }\r\n}\r\n\r\n/* Empty State */\r\n.emptyContainer {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 4rem 2rem;\r\n  text-align: center;\r\n  min-height: 300px;\r\n}\r\n\r\n.emptyIcon {\r\n  width: 80px;\r\n  height: 80px;\r\n  color: #d1d5db;\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.emptyIcon svg {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.emptyTitle {\r\n  color: #1f2937;\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  margin: 0 0 0.75rem 0;\r\n}\r\n\r\n.emptyMessage {\r\n  color: #6b5b4d;\r\n  font-size: 1rem;\r\n  line-height: 1.5;\r\n  margin: 0;\r\n  max-width: 400px;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1200px) {\r\n  .productGrid,\r\n  .loadingGrid {\r\n    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n    gap: 1.5rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .productGrid,\r\n  .loadingGrid {\r\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\r\n    gap: 1rem;\r\n    padding: 1rem 0;\r\n  }\r\n  \r\n  .emptyContainer {\r\n    padding: 3rem 1rem;\r\n  }\r\n  \r\n  .emptyIcon {\r\n    width: 60px;\r\n    height: 60px;\r\n  }\r\n  \r\n  .emptyTitle {\r\n    font-size: 1.25rem;\r\n  }\r\n  \r\n  .emptyMessage {\r\n    font-size: 0.9rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .productGrid,\r\n  .loadingGrid {\r\n    grid-template-columns: 1fr;\r\n    gap: 1rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;AAQA;;;;AAIA;;;;;;AAMA;;;;;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;;;;;AASA;;;;;;;;;AAUA;;;;;;;;;AAUA;;;;;;;AAQA;;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;AAKA;;;;;;;AAOA;;;;;;;;AASA;EACE;;;;;;AAOF;EACE;;;;;;EAOA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;AAKF;EACE", "debugId": null}}, {"offset": {"line": 453, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/ProductImageGallery/productImageGallery.module.css"], "sourcesContent": ["/* Product Image Gallery - Sharp Rectangular Design */\r\n.imageGallery {\r\n  width: 100%;\r\n}\r\n\r\n/* Main Image Container */\r\n.mainImageContainer {\r\n  position: relative;\r\n  background: #f8f8f8;\r\n  border: 2px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n  overflow: hidden;\r\n  aspect-ratio: 1;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.mainImage {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  cursor: zoom-in;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.mainImage.zoomed {\r\n  cursor: zoom-out;\r\n}\r\n\r\n/* Navigation Buttons */\r\n.navButton {\r\n  position: absolute;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  background: rgba(0, 0, 0, 0.7);\r\n  color: white;\r\n  border: none;\r\n  width: 50px;\r\n  height: 50px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n.navButton:hover {\r\n  background: rgba(0, 0, 0, 0.9);\r\n}\r\n\r\n.prevButton {\r\n  left: 10px;\r\n}\r\n\r\n.nextButton {\r\n  right: 10px;\r\n}\r\n\r\n/* Image Counter */\r\n.imageCounter {\r\n  position: absolute;\r\n  bottom: 15px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n  background: rgba(0, 0, 0, 0.8);\r\n  color: white;\r\n  padding: 0.5rem 1rem;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n/* Zoom Indicator */\r\n.zoomIndicator {\r\n  position: absolute;\r\n  top: 15px;\r\n  right: 15px;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  color: #1f2937;\r\n  padding: 0.5rem;\r\n  font-size: 0.8rem;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  border: 1px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n/* Thumbnail Container */\r\n/* .thumbnailContainer {\r\n  margin-top: 1rem;\r\n}\r\n\r\n.thumbnailGrid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));\r\n  gap: 0.5rem;\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.thumbnail {\r\n  background: none;\r\n  border: 2px solid #ddd;\r\n  border-radius: 0;\r\n  padding: 0;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  aspect-ratio: 1;\r\n  overflow: hidden;\r\n}\r\n\r\n.thumbnailImage {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n} */\r\n\r\n.thumbnailContainer {\r\n  overflow-x: auto;\r\n  overflow-y: hidden;\r\n  white-space: nowrap;\r\n  padding: 8px 0;\r\n}\r\n\r\n.thumbnailGrid {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.thumbnail {\r\n  display: inline-block;\r\n  border: none;\r\n  background: none;\r\n  padding: 0;\r\n  cursor: pointer;\r\n  outline: none;\r\n}\r\n\r\n.thumbnailImage {\r\n  width: 60px;\r\n  height: 60px;\r\n  object-fit: cover;\r\n  border: 2px solid transparent;\r\n  border-radius: 4px;\r\n  transition: border 0.3s;\r\n}\r\n\r\n.activeThumbnail .thumbnailImage {\r\n  border-color: #2563eb; /* Highlight active */\r\n}\r\n\r\n.thumbnail:hover {\r\n  border-color: #2563eb;\r\n}\r\n\r\n.thumbnail.activeThumbnail {\r\n  border-color: #2563eb;\r\n  border-width: 3px;\r\n}\r\n\r\n\r\n\r\n/* Zoom Overlay */\r\n.zoomOverlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.9);\r\n  z-index: 1000;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: zoom-out;\r\n}\r\n\r\n.zoomedImageContainer {\r\n  position: relative;\r\n  max-width: 90vw;\r\n  max-height: 90vh;\r\n}\r\n\r\n.zoomedImage {\r\n  max-width: 100%;\r\n  max-height: 100%;\r\n  object-fit: contain;\r\n}\r\n\r\n.closeZoomButton {\r\n  position: absolute;\r\n  top: -50px;\r\n  right: 0;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  color: #1f2937;\r\n  border: none;\r\n  width: 40px;\r\n  height: 40px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 0; /* Sharp corners */\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.closeZoomButton:hover {\r\n  background: white;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 768px) {\r\n  .navButton {\r\n    width: 40px;\r\n    height: 40px;\r\n  }\r\n  \r\n  .prevButton {\r\n    left: 5px;\r\n  }\r\n  \r\n  .nextButton {\r\n    right: 5px;\r\n  }\r\n  \r\n  .imageCounter {\r\n    bottom: 10px;\r\n    font-size: 0.8rem;\r\n    padding: 0.25rem 0.75rem;\r\n  }\r\n  \r\n  .zoomIndicator {\r\n    top: 10px;\r\n    right: 10px;\r\n    padding: 0.25rem;\r\n    font-size: 0.7rem;\r\n  }\r\n  \r\n  .thumbnailGrid {\r\n    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));\r\n    gap: 0.25rem;\r\n  }\r\n  \r\n  .zoomedImageContainer {\r\n    max-width: 95vw;\r\n    max-height: 95vh;\r\n  }\r\n  \r\n  .closeZoomButton {\r\n    top: -40px;\r\n    width: 35px;\r\n    height: 35px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .mainImageContainer {\r\n    margin-bottom: 0.5rem;\r\n  }\r\n  \r\n  .navButton {\r\n    width: 35px;\r\n    height: 35px;\r\n  }\r\n  \r\n  .thumbnailGrid {\r\n    grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;AAKA;;;;;;;;;;AAUA;;;;;;;;AAQA;;;;AAKA;;;;;;;;;;;;;;;;;AAiBA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;;;;;;;;;AAcA;;;;;;;;;;;;;;;AA6CA;;;;;;AAOA;;;;;AAKA;;;;;;;;;AASA;;;;;;;;;AASA;;;;AAQA;;;;;AAQA;;;;;;;;;;;AAcA;;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;;;;;;AAiBA;;;;AAKA;EACE;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;;;EAOA;;;;;EAKA;;;;;EAKA;;;;;;;AAOF;EACE;;;;EAIA;;;;;EAKA", "debugId": null}}, {"offset": {"line": 679, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/ProductSpecifications/productSpecifications.module.css"], "sourcesContent": ["/* Product Specifications - Sharp Rectangular Design */\r\n.specificationsContainer {\r\n  /* margin: 3rem 0; */\r\n  /* margin: 2rem 0 0 0 auto;\r\n  width: 100%;\r\n  max-width: 40vw; */\r\n\r\n  margin-top: 2rem;\r\n  /* margin-left: auto; */\r\n  width: 100%;\r\n  /* max-width: 35vw; */\r\n  /* margin-top: -20rem; */\r\n  margin-bottom: 15rem;\r\n}\r\n\r\n/* Section Styles */\r\n.section {\r\n  border: 0px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n  margin-bottom: 1rem;\r\n  background: white;\r\n}\r\n\r\n.sectionHeader {\r\n  width: 100%;\r\n  background: #f5f5f5;\r\n  border: none;\r\n  border-bottom: 1px solid #ddd;\r\n  padding: 1.5rem;\r\n  text-align: left;\r\n  cursor: pointer;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n  color:rgb(14, 14, 14);\r\n  transition: background-color 0.3s ease;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n.sectionHeader:hover {\r\n  background: #eeeeee;\r\n}\r\n\r\n.sectionHeader.active {\r\n    /* background: #f5f5f5; */\r\n  background: #f5f5f5;\r\n  color: black;\r\n}\r\n\r\n.toggleIcon {\r\n  font-size: 1.5rem;\r\n  font-weight: bold;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.sectionContent {\r\n  padding: 2rem;\r\n  border-top: 1px solid #ddd;\r\n}\r\n\r\n/* Specifications Grid - Simple Lines Style */\r\n.specGrid {\r\n  display: block;\r\n  width: 100%;\r\n  background: white;\r\n  margin-top: 1rem;\r\n}\r\n.keySpecsTable {\r\n  width: 100%;\r\n  max-width: 600px;\r\n  font-family: \"Arial\", sans-serif;\r\n  font-size: 15px;\r\n  color: #111;\r\n  border-collapse: collapse;\r\n  margin-top: 2rem;\r\n  border-top: 1px solid #e5e7eb;\r\n}\r\n\r\n/* .specRow {\r\n  display: flex;\r\n  align-items: baseline;\r\n  padding: 0.5rem 0;\r\n  border-bottom: 1px solid #e5e7eb;\r\n}\r\n\r\n.specRow:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.specLabel {\r\n  font-weight: 600;\r\n  color: #1f2937;\r\n  font-size: 1rem;\r\n  margin-right: 0.5rem;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.specValue {\r\n  color: #1f2937;\r\n  font-size: 1rem;\r\n  flex: 1;\r\n  margin-left: 1rem;\r\n  text-align: left;\r\n} */\r\n\r\n.specRow {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  padding: 6px 0;\r\n  border-bottom: 1px solid #d1d5db; /* Tailwind gray-300 */\r\n}\r\n\r\n.specRow:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.specLabel {\r\n  width: 50%;\r\n  font-weight: 600;\r\n  font-size: 0.875rem; /* text-sm */\r\n  color: #111827; /* Tailwind gray-900 */\r\n  line-height: 1.5;\r\n}\r\n\r\n.specValue {\r\n  width: 50%;\r\n  font-size: 0.875rem;\r\n  color: #111827;\r\n  text-align: left;\r\n  line-height: 1.5;\r\n}\r\n\r\n\r\n.specValue.inStock {\r\n  color: #28a745;\r\n  font-weight: 600;\r\n}\r\n\r\n.specValue.outOfStock {\r\n  color: #dc3545;\r\n  font-weight: 600;\r\n}\r\n\r\n/* Tags */\r\n.tagContainer {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.tag {\r\n  background: #1e40af;\r\n  color: white;\r\n  padding: 0.25rem 0.75rem;\r\n  font-size: 0.8rem;\r\n  border-radius: 0; /* Sharp corners */\r\n  border: 1px solid #1e40af;\r\n}\r\n\r\n/* Details Content */\r\n.detailsContent {\r\n  line-height: 1.6;\r\n}\r\n\r\n.description {\r\n  color: #555;\r\n  margin-bottom: 2rem;\r\n  font-size: 1rem;\r\n}\r\n\r\n.featureList h4 {\r\n  color: #1e40af;\r\n  margin-bottom: 1rem;\r\n  font-size: 1.1rem;\r\n}\r\n\r\n.featureList ul {\r\n  list-style: none;\r\n  padding: 0;\r\n}\r\n\r\n.featureList li {\r\n  padding: 0.5rem 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  position: relative;\r\n  padding-left: 1.5rem;\r\n}\r\n\r\n.featureList li:before {\r\n  content: '✓';\r\n  position: absolute;\r\n  left: 0;\r\n  color: #1e40af;\r\n  font-weight: bold;\r\n}\r\n\r\n.featureList li:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n/* Care Content */\r\n.careContent {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 3rem;\r\n}\r\n\r\n.careSection h4,\r\n.downloadSection h4 {\r\n  color: #1e40af;\r\n  margin-bottom: 1rem;\r\n  font-size: 1.1rem;\r\n}\r\n\r\n.careSection ul {\r\n  list-style: none;\r\n  padding: 0;\r\n}\r\n\r\n.careSection li {\r\n  padding: 0.5rem 0;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  position: relative;\r\n  padding-left: 1.5rem;\r\n}\r\n\r\n.careSection li:before {\r\n  content: '•';\r\n  position: absolute;\r\n  left: 0;\r\n  color: #1e40af;\r\n  font-weight: bold;\r\n}\r\n\r\n.careSection li:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n/* Download Links */\r\n.downloadLinks {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1rem;\r\n}\r\n\r\n.downloadLink {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.75rem;\r\n  padding: 1rem;\r\n  border: 2px solid #1e40af;\r\n  color: #1e40af;\r\n  text-decoration: none;\r\n  transition: all 0.3s ease;\r\n  border-radius: 0; /* Sharp corners */\r\n  font-weight: 500;\r\n}\r\n\r\n.downloadLink:hover {\r\n  background: #1e40af;\r\n  color: white;\r\n}\r\n\r\n/* Share Section */\r\n.shareSection {\r\n  margin-top: 2rem;\r\n  padding: 1.5rem;\r\n  border: 2px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n  background: #f8f8f8;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n}\r\n\r\n.shareLabel {\r\n  font-weight: 600;\r\n  color: #1e40af;\r\n  font-size: 1rem;\r\n}\r\n\r\n.shareButtons {\r\n  display: flex;\r\n  gap: 0.75rem;\r\n}\r\n\r\n.shareButton {\r\n  background: #1e40af;\r\n  color: white;\r\n  border: none;\r\n  width: 40px;\r\n  height: 40px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n.shareButton:hover {\r\n  background: #1d4ed8;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .careContent {\r\n    grid-template-columns: 1fr;\r\n    gap: 2rem;\r\n  }\r\n}\r\n\r\n\r\n  @media (max-width: 768px) {\r\n      \r\n  .shareSection {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    text-align: center;\r\n  }\r\n  \r\n  .shareButtons {\r\n    justify-content: center;\r\n  }  \r\n\r\n  .downloadLink {\r\n    padding: 0.75rem;\r\n    font-size: 0.9rem;\r\n  }\r\n  \r\n  .shareButton {\r\n    width: 35px;\r\n    height: 35px;\r\n  }\r\n  \r\n  .tagContainer {\r\n    gap: 0.25rem;\r\n  }\r\n  \r\n  .tag {\r\n    font-size: 0.75rem;\r\n    padding: 0.2rem 0.5rem;\r\n  }\r\n  .specRow {\r\n    display: flex !important;\r\n    flex-direction: row !important;\r\n    justify-content: space-between;\r\n    align-items: flex-start;\r\n     /* align-items: baseline; */\r\n    gap: 0.5rem;\r\n    grid-template-columns: 1fr;\r\n    flex-wrap: wrap;\r\n    padding: 0.4rem 0;\r\n    border-bottom: 1px solid #e5e7eb;\r\n    line-height: 1.3;\r\n  }\r\n\r\n  .specLabel {\r\n    font-weight: 600;\r\n    flex: 0 0 auto;\r\n    min-width: 100px;\r\n    font-size: 0.9rem;\r\n  }\r\n\r\n  .specGrid {\r\n    display: block;\r\n    margin-top: 0.5rem;\r\n  }\r\n\r\n\r\n  .specRow:last-child {\r\n    border-bottom: none;\r\n  }\r\n\r\n  .specLabel {\r\n    font-weight: 600;\r\n    color: #1f2937;\r\n    font-size: 0.9rem;\r\n    margin-right: 0.4rem;\r\n    flex-shrink: 0;\r\n  }\r\n\r\n  .specValue {\r\n    color: #1f2937;\r\n    font-size: 0.9rem;\r\n    flex: 1;\r\n    word-break: break-word;\r\n  }\r\n\r\n  .sectionHeader {\r\n    padding: 1rem;\r\n    font-size: 1rem;\r\n  }\r\n\r\n  .sectionContent {\r\n    padding: 1rem;\r\n  }\r\n\r\n}\r\n\r\n\r\n"], "names": [], "mappings": "AACA;;;;;;AAeA;;;;;;;AAOA;;;;;;;;;;;;;;;;;;AAkBA;;;;AAIA;;;;;AAMA;;;;;;AAMA;;;;;AAMA;;;;;;;AAMA;;;;;;;;;;;AAsCA;;;;;;;;AAQA;;;;AAIA;;;;;;;;AAQA;;;;;;;;AASA;;;;;AAKA;;;;;AAMA;;;;;;AAMA;;;;;;;;;AAUA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;;AAOA;;;;;;;;AAQA;;;;AAKA;;;;;;AAMA;;;;;;AAOA;;;;;AAKA;;;;;;AAOA;;;;;;;;AAQA;;;;AAKA;;;;;;AAMA;;;;;;;;;;;;;AAaA;;;;;AAMA;;;;;;;;;;;AAWA;;;;;;AAMA;;;;;AAKA;;;;;;;;;;;;;;AAcA;;;;;AAMA;EACE;;;;;;AAOA;EAEA;;;;;;EAMA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;EAIA;;;;;;;;;;;;;EAcA;;;;;;;EAOA;;;;;EAMA;;;;EAIA;;;;;;;;EAQA;;;;;;;EAOA;;;;;EAKA", "debugId": null}}, {"offset": {"line": 1033, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/components/products/RelatedProducts/relatedProducts.module.css"], "sourcesContent": ["/* Related Products - Sharp Rectangular Design */\r\n.relatedProducts {\r\n  margin: 4rem 0;\r\n  padding: 2rem 0;\r\n  border-top: 2px solid #ddd;\r\n}\r\n\r\n.sectionHeader {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.sectionTitle {\r\n  font-size: 2rem;\r\n  font-weight: 700;\r\n  color: #1f2937;\r\n  margin: 0;\r\n}\r\n\r\n.scrollControls {\r\n  display: flex;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.scrollButton {\r\n  background: #2563eb;\r\n  color: white;\r\n  border: 2px solid #2563eb;\r\n  width: 45px;\r\n  height: 45px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n.scrollButton:hover:not(.disabled) {\r\n  background: white;\r\n  color: #2563eb;\r\n}\r\n\r\n.scrollButton.disabled {\r\n  background: #ccc;\r\n  border-color: #ccc;\r\n  cursor: not-allowed;\r\n  opacity: 0.5;\r\n}\r\n\r\n/* Products Container */\r\n.productsContainer {\r\n  overflow-x: auto;\r\n  overflow-y: hidden;\r\n  scrollbar-width: none; /* Firefox */\r\n  -ms-overflow-style: none; /* IE and Edge */\r\n}\r\n\r\n.productsContainer::-webkit-scrollbar {\r\n  display: none; /* Chrome, Safari, Opera */\r\n}\r\n\r\n.productsGrid {\r\n  display: flex;\r\n  gap: 1.5rem;\r\n  padding-bottom: 1rem;\r\n}\r\n\r\n/* Product Card */\r\n.productCard {\r\n  flex: 0 0 300px;\r\n  background: white;\r\n  border: 2px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.productCard:hover {\r\n  border-color: #2563eb;\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 24px rgba(37, 99, 235, 0.15);\r\n}\r\n\r\n.productLink {\r\n  text-decoration: none;\r\n  color: inherit;\r\n  display: block;\r\n}\r\n\r\n/* Image Container */\r\n.imageContainer {\r\n  position: relative;\r\n  aspect-ratio: 1;\r\n  overflow: hidden;\r\n  background: #f8f8f8;\r\n}\r\n\r\n.productImage {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.productCard:hover .productImage {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.outOfStockOverlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.7);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-weight: 600;\r\n  font-size: 1.1rem;\r\n}\r\n\r\n/* Product Info */\r\n.productInfo {\r\n  padding: 1.5rem;\r\n}\r\n\r\n.productName {\r\n  font-size: 1.2rem;\r\n  font-weight: 700;\r\n  color: #1f2937;\r\n  margin: 0 0 1rem 0;\r\n  line-height: 1.3;\r\n}\r\n\r\n.productDetails {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.productCode {\r\n  display: block;\r\n  font-family: 'Courier New', monospace;\r\n  font-size: 0.85rem;\r\n  color: #666;\r\n  background: #f5f5f5;\r\n  padding: 0.25rem 0.5rem;\r\n  border: 1px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n  margin-bottom: 0.75rem;\r\n  width: fit-content;\r\n}\r\n\r\n.availability {\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.inStock {\r\n  color: #28a745;\r\n  font-weight: 600;\r\n}\r\n\r\n.outOfStock {\r\n  color: #dc3545;\r\n  font-weight: 600;\r\n}\r\n\r\n.priceContainer {\r\n  border-top: 1px solid #eee;\r\n  padding-top: 1rem;\r\n}\r\n\r\n.price {\r\n  font-size: 1.3rem;\r\n  font-weight: 700;\r\n  color: #1f2937;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .productCard {\r\n    flex: 0 0 280px;\r\n  }\r\n  \r\n  .sectionTitle {\r\n    font-size: 1.75rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .relatedProducts {\r\n    margin: 3rem 0;\r\n  }\r\n  \r\n  .sectionHeader {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 1rem;\r\n  }\r\n  \r\n  .scrollControls {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .productCard {\r\n    flex: 0 0 250px;\r\n  }\r\n  \r\n  .productInfo {\r\n    padding: 1rem;\r\n  }\r\n  \r\n  .productName {\r\n    font-size: 1.1rem;\r\n  }\r\n  \r\n  .price {\r\n    font-size: 1.2rem;\r\n  }\r\n  \r\n  .sectionTitle {\r\n    font-size: 1.5rem;\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .productCard {\r\n    flex: 0 0 220px;\r\n  }\r\n  \r\n  .productInfo {\r\n    padding: 0.75rem;\r\n  }\r\n  \r\n  .productName {\r\n    font-size: 1rem;\r\n  }\r\n  \r\n  .price {\r\n    font-size: 1.1rem;\r\n  }\r\n  \r\n  .productCode {\r\n    font-size: 0.8rem;\r\n    padding: 0.2rem 0.4rem;\r\n  }\r\n  \r\n  .scrollButton {\r\n    width: 40px;\r\n    height: 40px;\r\n  }\r\n}\r\n\r\n/* Accessibility */\r\n@media (prefers-reduced-motion: reduce) {\r\n  .productCard {\r\n    transition: none;\r\n  }\r\n  \r\n  .productCard:hover {\r\n    transform: none;\r\n  }\r\n  \r\n  .productImage {\r\n    transition: none;\r\n  }\r\n  \r\n  .productCard:hover .productImage {\r\n    transform: none;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;AAMA;;;;;;;AAOA;;;;;;;AAOA;;;;;AAKA;;;;;;;;;;;;;;AAcA;;;;;AAKA;;;;;;;AAQA;;;;;;AAOA;;;;AAIA;;;;;;AAOA;;;;;;;;;AASA;;;;;;AAMA;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;AAIA;;;;;;;;;;;;AAgBA;;;;AAIA;;;;;;;;AAQA;;;;AAIA;;;;;;;;;;;;;AAaA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;AAOA;EACE;;;;EAIA;;;;;AAKF;EACE;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;AAMF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;AAOF;EACE;;;;EAIA;;;;EAIA;;;;EAIA", "debugId": null}}, {"offset": {"line": 1300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/app/collections/%5Bid%5D/collectionPage.module.css"], "sourcesContent": ["/* Collection Page - Sharp Rectangular Design */\r\n.collectionPage {\r\n  min-height: 100vh;\r\n  background: #ffffff;\r\n}\r\n\r\n.container {\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  padding: 6rem 2rem 2rem;\r\n}\r\n\r\n/* Loading States */\r\n.loadingContainer {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: 60vh;\r\n  color: #1e40af;\r\n}\r\n\r\n.loadingSpinner {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: 3px solid #f3f3f3;\r\n  border-top: 3px solid #1e40af;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.errorContainer {\r\n  text-align: center;\r\n  padding: 4rem 2rem;\r\n  color: #1e40af;\r\n}\r\n\r\n.errorContainer h1 {\r\n  font-size: 2rem;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n/* Collection Header */\r\n.collectionHeader {\r\n  margin-bottom: 3rem;\r\n  padding: 2rem;\r\n  background: #f8f8f8;\r\n  border: 2px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n.headerContent {\r\n  text-align: center;\r\n}\r\n\r\n.collectionTitle {\r\n  font-size: 3rem;\r\n  font-weight: 700;\r\n  color: #1e40af;\r\n  margin-bottom: 1rem;\r\n  line-height: 1.2;\r\n}\r\n\r\n.collectionDescription {\r\n  font-size: 1.2rem;\r\n  color: #666;\r\n  margin-bottom: 1.5rem;\r\n  max-width: 800px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  line-height: 1.6;\r\n}\r\n\r\n.collectionMeta {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 2rem;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.productCount {\r\n  font-weight: 600;\r\n  color: #1e40af;\r\n  background: white;\r\n  padding: 0.5rem 1rem;\r\n  border: 2px solid #1e40af;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n.collectionLevel {\r\n  font-weight: 600;\r\n  color: #666;\r\n  background: white;\r\n  padding: 0.5rem 1rem;\r\n  border: 2px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n/* Filter Section */\r\n.filterSection {\r\n  margin-bottom: 2rem;\r\n  background: white;\r\n  border: 2px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n  overflow: hidden;\r\n}\r\n\r\n.searchBar {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n  padding: 1.5rem;\r\n  background: #fafafa;\r\n  border-bottom: 1px solid #ddd;\r\n}\r\n\r\n.searchInput {\r\n  flex: 1;\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.searchIcon {\r\n  position: absolute;\r\n  left: 1rem;\r\n  color: #666;\r\n  z-index: 1;\r\n}\r\n\r\n.searchField {\r\n  width: 100%;\r\n  padding: 1rem 1rem 1rem 3rem;\r\n  border: 2px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n  font-size: 1rem;\r\n  background: white;\r\n  transition: border-color 0.3s ease;\r\n}\r\n\r\n.searchField:focus {\r\n  outline: none;\r\n  border-color: #1e40af;\r\n}\r\n\r\n.filterToggle {\r\n  background: #1e40af;\r\n  color: white;\r\n  border: 2px solid #1e40af;\r\n  padding: 1rem 1.5rem;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  font-weight: 600;\r\n  transition: all 0.3s ease;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n.filterToggle:hover {\r\n  background: white;\r\n  color: #1e40af;\r\n}\r\n\r\n.filterToggle.active {\r\n  background: #1d4ed8;\r\n}\r\n\r\n/* Advanced Filters */\r\n.advancedFilters {\r\n  padding: 2rem;\r\n  background: white;\r\n  border-top: 1px solid #ddd;\r\n}\r\n\r\n.filterGrid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\r\n  gap: 2rem;\r\n  align-items: end;\r\n}\r\n\r\n.filterGroup {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.filterLabel {\r\n  font-weight: 600;\r\n  color: #1e40af;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.priceRange {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.priceInput {\r\n  flex: 1;\r\n  padding: 0.75rem;\r\n  border: 2px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.priceInput:focus {\r\n  outline: none;\r\n  border-color: #1e40af;\r\n}\r\n\r\n.checkboxLabel {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  font-weight: 600;\r\n  color: #1e40af;\r\n  cursor: pointer;\r\n}\r\n\r\n.checkbox {\r\n  width: 18px;\r\n  height: 18px;\r\n  accent-color: #1e40af;\r\n}\r\n\r\n.sortControls {\r\n  display: flex;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.sortSelect {\r\n  flex: 1;\r\n  padding: 0.75rem;\r\n  border: 2px solid #ddd;\r\n  border-radius: 0; /* Sharp corners */\r\n  background: white;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.sortSelect:focus {\r\n  outline: none;\r\n  border-color: #1e40af;\r\n}\r\n\r\n.sortDirection {\r\n  background: #1e40af;\r\n  color: white;\r\n  border: 2px solid #1e40af;\r\n  width: 40px;\r\n  height: 40px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n  border-radius: 0; /* Sharp corners */\r\n}\r\n\r\n.sortDirection:hover {\r\n  background: white;\r\n  color: #1e40af;\r\n}\r\n\r\n.sortDirection.desc {\r\n  transform: rotate(180deg);\r\n}\r\n\r\n.clearFilters {\r\n  background: #dc3545;\r\n  color: white;\r\n  border: 2px solid #dc3545;\r\n  padding: 0.75rem 1rem;\r\n  cursor: pointer;\r\n  font-weight: 600;\r\n  transition: all 0.3s ease;\r\n  border-radius: 0; /* Sharp corners */\r\n  width: 100%;\r\n}\r\n\r\n.clearFilters:hover {\r\n  background: white;\r\n  color: #dc3545;\r\n}\r\n\r\n/* Products Section */\r\n.productsSection {\r\n  margin-top: 2rem;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .collectionTitle {\r\n    font-size: 2.5rem;\r\n  }\r\n  \r\n  .filterGrid {\r\n    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));\r\n    gap: 1.5rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .container {\r\n    padding: 1rem;\r\n  }\r\n  \r\n  .collectionHeader {\r\n    padding: 1.5rem;\r\n    margin-bottom: 2rem;\r\n  }\r\n  \r\n  .collectionTitle {\r\n    font-size: 2rem;\r\n  }\r\n  \r\n  .collectionDescription {\r\n    font-size: 1rem;\r\n  }\r\n  \r\n  .collectionMeta {\r\n    flex-direction: column;\r\n    gap: 1rem;\r\n  }\r\n  \r\n  .searchBar {\r\n    flex-direction: column;\r\n    gap: 1rem;\r\n    padding: 1rem;\r\n  }\r\n  \r\n  .searchInput {\r\n    width: 100%;\r\n  }\r\n  \r\n  .filterToggle {\r\n    width: 100%;\r\n    justify-content: center;\r\n  }\r\n  \r\n  .filterGrid {\r\n    grid-template-columns: 1fr;\r\n    gap: 1rem;\r\n  }\r\n  \r\n  .priceRange {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .sortControls {\r\n    flex-direction: column;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .collectionTitle {\r\n    font-size: 1.75rem;\r\n  }\r\n  \r\n  .advancedFilters {\r\n    padding: 1rem;\r\n  }\r\n  \r\n  .searchField {\r\n    padding: 0.75rem 0.75rem 0.75rem 2.5rem;\r\n  }\r\n  \r\n  .filterToggle {\r\n    padding: 0.75rem 1rem;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;AAKA;;;;;;AAOA;;;;;;;;;AASA;;;;;;;;;;AAUA;;;;;;;;;;AAKA;;;;;;AAMA;;;;;AAMA;;;;;;;;AAQA;;;;AAIA;;;;;;;;AAQA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;;;;AASA;;;;;;;;;AAUA;;;;;;;;AAQA;;;;;;;;;AASA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;AAUA;;;;;AAKA;;;;;;;;;;;;;;AAcA;;;;;AAKA;;;;AAKA;;;;;;AAMA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;;;AAQA;;;;;AAKA;;;;;;;;;AASA;;;;;;AAMA;;;;;AAKA;;;;;;;;;AASA;;;;;AAKA;;;;;;;;;;;;;;AAcA;;;;;AAKA;;;;AAIA;;;;;;;;;;;;AAYA;;;;;AAMA;;;;AAKA;EACE;;;;EAIA;;;;;;AAMF;EACE;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;EAMA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;AAKF;EACE;;;;EAIA;;;;EAIA;;;;EAIA", "debugId": null}}]}