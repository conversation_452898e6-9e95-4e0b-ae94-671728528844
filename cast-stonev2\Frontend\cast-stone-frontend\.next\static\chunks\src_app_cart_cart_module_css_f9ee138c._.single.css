/* [project]/src/app/cart/cart.module.css [app-client] (css) */
.cart-module__-RJi4G__container {
  max-width: 1200px;
  min-height: 80vh;
  margin: 0 auto;
  padding: 8rem 1rem 2rem;
}

.cart-module__-RJi4G__header {
  text-align: center;
  border-bottom: 2px solid #f3f4f6;
  margin-bottom: 3rem;
  padding-bottom: 2rem;
}

.cart-module__-RJi4G__title {
  color: #1f2937;
  margin: 0 0 .5rem;
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
}

.cart-module__-RJi4G__subtitle {
  color: #4b5563;
  margin: 0;
  font-size: 1.1rem;
}

.cart-module__-RJi4G__loadingContainer {
  color: #4b5563;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  display: flex;
}

.cart-module__-RJi4G__loadingSpinner {
  border: 3px solid #f3f4f6;
  border-top-color: #2563eb;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  margin-bottom: 1rem;
  animation: 1s linear infinite cart-module__-RJi4G__spin;
}

@keyframes cart-module__-RJi4G__spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.cart-module__-RJi4G__emptyCart {
  text-align: center;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 2rem;
  display: flex;
}

.cart-module__-RJi4G__emptyIcon {
  color: #d1d5db;
  width: 120px;
  height: 120px;
  margin-bottom: 2rem;
}

.cart-module__-RJi4G__emptyIcon svg {
  stroke-width: 1.5px;
  width: 100%;
  height: 100%;
}

.cart-module__-RJi4G__emptyTitle {
  color: #1f2937;
  margin: 0 0 1rem;
  font-size: 2rem;
  font-weight: 700;
}

.cart-module__-RJi4G__emptyMessage {
  color: #4b5563;
  max-width: 500px;
  margin: 0 0 2rem;
  font-size: 1.1rem;
  line-height: 1.6;
}

.cart-module__-RJi4G__shopNowBtn {
  color: #fff;
  background: #2563eb;
  border-radius: 6px;
  align-items: center;
  gap: .5rem;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all .3s;
  display: inline-flex;
}

.cart-module__-RJi4G__shopNowBtn:hover {
  background: #1d4ed8;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px #2563eb4d;
}

.cart-module__-RJi4G__shopIcon {
  stroke-width: 2px;
  width: 20px;
  height: 20px;
}

.cart-module__-RJi4G__errorMessage {
  color: #dc2626;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  align-items: center;
  gap: .75rem;
  margin-bottom: 2rem;
  padding: 1rem;
  display: flex;
}

.cart-module__-RJi4G__errorIcon {
  stroke-width: 2px;
  flex-shrink: 0;
  width: 20px;
  height: 20px;
}

.cart-module__-RJi4G__cartContent {
  grid-template-columns: 1fr 350px;
  gap: 3rem;
  margin-bottom: 3rem;
  display: grid;
}

.cart-module__-RJi4G__cartItems {
  min-width: 0;
}

.cart-module__-RJi4G__itemsHeader {
  border-bottom: 1px solid #e5e7eb;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  display: flex;
}

.cart-module__-RJi4G__itemsHeader h2 {
  color: #1f2937;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.cart-module__-RJi4G__continueShoppingLink {
  color: #2563eb;
  font-weight: 600;
  text-decoration: none;
  transition: color .2s;
}

.cart-module__-RJi4G__continueShoppingLink:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

.cart-module__-RJi4G__itemsList {
  flex-direction: column;
  gap: 1.5rem;
  display: flex;
}

.cart-module__-RJi4G__additionalActions {
  border-top: 2px solid #f3f4f6;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  padding-top: 2rem;
  display: grid;
}

.cart-module__-RJi4G__helpSection, .cart-module__-RJi4G__shippingInfo {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
}

.cart-module__-RJi4G__helpSection h3, .cart-module__-RJi4G__shippingInfo h3 {
  color: #1f2937;
  margin: 0 0 1rem;
  font-size: 1.25rem;
  font-weight: 700;
}

.cart-module__-RJi4G__helpSection p {
  color: #4b5563;
  margin: 0;
  line-height: 1.6;
}

.cart-module__-RJi4G__contactLink {
  color: #2563eb;
  font-weight: 600;
  text-decoration: none;
}

.cart-module__-RJi4G__contactLink:hover {
  text-decoration: underline;
}

.cart-module__-RJi4G__shippingInfo ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.cart-module__-RJi4G__shippingInfo li {
  color: #4b5563;
  border-bottom: 1px solid #e5e7eb;
  padding: .5rem 0 .5rem 1.5rem;
  position: relative;
}

.cart-module__-RJi4G__shippingInfo li:last-child {
  border-bottom: none;
}

.cart-module__-RJi4G__shippingInfo li:before {
  content: "✓";
  color: #059669;
  font-weight: bold;
  position: absolute;
  left: 0;
}

@media (width <= 1024px) {
  .cart-module__-RJi4G__cartContent {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

@media (width <= 768px) {
  .cart-module__-RJi4G__container {
    padding: 1rem;
  }

  .cart-module__-RJi4G__header {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
  }

  .cart-module__-RJi4G__title {
    font-size: 2rem;
  }

  .cart-module__-RJi4G__subtitle {
    font-size: 1rem;
  }

  .cart-module__-RJi4G__cartContent {
    gap: 1.5rem;
  }

  .cart-module__-RJi4G__additionalActions {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .cart-module__-RJi4G__helpSection, .cart-module__-RJi4G__shippingInfo {
    padding: 1rem;
  }

  .cart-module__-RJi4G__emptyIcon {
    width: 80px;
    height: 80px;
  }

  .cart-module__-RJi4G__emptyTitle {
    font-size: 1.5rem;
  }

  .cart-module__-RJi4G__emptyMessage {
    font-size: 1rem;
  }
}

@media (width <= 480px) {
  .cart-module__-RJi4G__container {
    padding: 6rem .5rem .5rem;
  }

  .cart-module__-RJi4G__title {
    font-size: 1.75rem;
  }

  .cart-module__-RJi4G__itemsHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: .5rem;
  }

  .cart-module__-RJi4G__shopNowBtn {
    padding: .875rem 1.5rem;
    font-size: 1rem;
  }
}

/*# sourceMappingURL=src_app_cart_cart_module_css_f9ee138c._.single.css.map*/