{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cast-stonev2/cast-stonev2/Frontend/cast-stone-frontend/src/app/checkout/checkout.module.css"], "sourcesContent": ["/* Checkout Page Styles - Magazine/Editorial Theme */\r\n.container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 8rem 1rem 2rem;\r\n  min-height: 80vh;\r\n}\r\n\r\n/* Header */\r\n.header {\r\n  text-align: center;\r\n  margin-bottom: 3rem;\r\n  padding-bottom: 2rem;\r\n  border-bottom: 2px solid #f3f4f6;\r\n}\r\n\r\n.title {\r\n  color: #1f2937;\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n  margin: 0 0 2rem 0;\r\n  line-height: 1.2;\r\n}\r\n\r\n/* Step Indicator */\r\n.stepIndicator {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 1rem;\r\n}\r\n\r\n.step {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.step span {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  background: #e5e7eb;\r\n  color: #6b7280;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: 600;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.step.active span {\r\n  background: #2563eb;\r\n  color: white;\r\n}\r\n\r\n.step label {\r\n  color: #4b5563;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.step.active label {\r\n  color: #1f2937;\r\n}\r\n\r\n.stepConnector {\r\n  width: 60px;\r\n  height: 2px;\r\n  background: #e5e7eb;\r\n}\r\n\r\n/* Checkout Content */\r\n.checkoutContent {\r\n  display: grid;\r\n  grid-template-columns: 1fr 400px;\r\n  gap: 3rem;\r\n}\r\n\r\n/* Main Content */\r\n.mainContent {\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  padding: 2rem;\r\n}\r\n\r\n.sectionTitle {\r\n  color: #1f2937;\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  margin: 0 0 2rem 0;\r\n  padding-bottom: 1rem;\r\n  border-bottom: 1px solid #e5e7eb;\r\n}\r\n\r\n/* Form Styles */\r\n.formGrid {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 1.5rem;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.formGroup {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.formGroup.fullWidth {\r\n  grid-column: 1 / -1;\r\n}\r\n\r\n.formGroup label {\r\n  color: #1f2937;\r\n  font-weight: 600;\r\n  margin-bottom: 0.5rem;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.formGroup input,\r\n.formGroup select {\r\n  padding: 0.75rem;\r\n  border: 2px solid #e5e7eb;\r\n  border-radius: 6px;\r\n  font-size: 1rem;\r\n  transition: border-color 0.2s ease;\r\n}\r\n\r\n.formGroup input:focus,\r\n.formGroup select:focus {\r\n  outline: none;\r\n  border-color: #2563eb;\r\n}\r\n\r\n/* Payment Methods */\r\n.paymentMethods {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 1rem;\r\n  margin-bottom: 2rem;\r\n}\r\n\r\n.paymentMethod {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n  padding: 1.5rem;\r\n  border: 2px solid #e5e7eb;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.paymentMethod:hover {\r\n  border-color: #2563eb;\r\n}\r\n\r\n.paymentMethod.selected {\r\n  border-color: #2563eb;\r\n  background: #eff6ff;\r\n}\r\n\r\n.paymentIcon {\r\n  font-size: 2rem;\r\n  width: 60px;\r\n  text-align: center;\r\n}\r\n\r\n.paymentInfo {\r\n  flex: 1;\r\n}\r\n\r\n.paymentInfo h3 {\r\n  color: #1f2937;\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n  margin: 0 0 0.25rem 0;\r\n}\r\n\r\n.paymentInfo p {\r\n  color: #4b5563;\r\n  font-size: 0.9rem;\r\n  margin: 0;\r\n}\r\n\r\n.radioButton input {\r\n  width: 20px;\r\n  height: 20px;\r\n  accent-color: #2563eb;\r\n}\r\n\r\n/* Step Actions */\r\n.stepActions {\r\n  display: flex;\r\n  gap: 1rem;\r\n  justify-content: flex-end;\r\n  padding-top: 2rem;\r\n  border-top: 1px solid #e5e7eb;\r\n}\r\n\r\n.nextBtn,\r\n.placeOrderBtn {\r\n  padding: 1rem 2rem;\r\n  background: #2563eb;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 6px;\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.nextBtn:hover,\r\n.placeOrderBtn:hover:not(:disabled) {\r\n  background: #1d4ed8;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.placeOrderBtn:disabled {\r\n  opacity: 0.7;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.backBtn {\r\n  padding: 1rem 2rem;\r\n  background: transparent;\r\n  color: #4b5563;\r\n  border: 2px solid #e5e7eb;\r\n  border-radius: 6px;\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.backBtn:hover {\r\n  border-color: #2563eb;\r\n  color: #2563eb;\r\n}\r\n\r\n/* Order Summary */\r\n.orderSummary {\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  padding: 2rem;\r\n  height: fit-content;\r\n  position: sticky;\r\n  top: 2rem;\r\n}\r\n\r\n.summaryTitle {\r\n  color: #1f2937;\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  margin: 0 0 1.5rem 0;\r\n  padding-bottom: 1rem;\r\n  border-bottom: 1px solid #e5e7eb;\r\n}\r\n\r\n/* Order Items */\r\n.orderItems {\r\n  margin-bottom: 1.5rem;\r\n}\r\n\r\n.orderItem {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 1rem;\r\n  padding: 1rem 0;\r\n  border-bottom: 1px solid #f3f4f6;\r\n}\r\n\r\n.orderItem:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.itemImage {\r\n  width: 60px;\r\n  height: 60px;\r\n  object-fit: cover;\r\n  border-radius: 6px;\r\n}\r\n\r\n.itemDetails {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.itemDetails h4 {\r\n  color: #1f2937;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  margin: 0 0 0.25rem 0;\r\n  line-height: 1.3;\r\n}\r\n\r\n.itemDetails p {\r\n  color: #4b5563;\r\n  font-size: 0.8rem;\r\n  margin: 0;\r\n}\r\n\r\n.itemPrice {\r\n  color: #1f2937;\r\n  font-weight: 600;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n/* Summary Totals */\r\n.summaryTotals {\r\n  padding-top: 1rem;\r\n  border-top: 1px solid #e5e7eb;\r\n}\r\n\r\n.summaryRow {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 0.75rem;\r\n}\r\n\r\n.summaryRow span:first-child {\r\n  color: #4b5563;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.summaryRow span:last-child {\r\n  color: #1f2937;\r\n  font-weight: 600;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.totalRow {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 1rem 0;\r\n  border-top: 2px solid #2563eb;\r\n  margin-top: 1rem;\r\n}\r\n\r\n.totalRow span:first-child {\r\n  color: #1f2937;\r\n  font-size: 1.1rem;\r\n  font-weight: 700;\r\n}\r\n\r\n.totalRow span:last-child {\r\n  color: #1f2937;\r\n  font-size: 1.25rem;\r\n  font-weight: 700;\r\n}\r\n\r\n/* Responsive Design */\r\n@media (max-width: 1024px) {\r\n  .checkoutContent {\r\n    grid-template-columns: 1fr;\r\n    gap: 2rem;\r\n  }\r\n\r\n  .orderSummary {\r\n    position: static;\r\n    order: -1;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .container {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .title {\r\n    font-size: 2rem;\r\n  }\r\n\r\n  .stepIndicator {\r\n    gap: 0.5rem;\r\n  }\r\n\r\n  .step span {\r\n    width: 35px;\r\n    height: 35px;\r\n    font-size: 0.9rem;\r\n  }\r\n\r\n  .stepConnector {\r\n    width: 40px;\r\n  }\r\n\r\n  .mainContent,\r\n  .orderSummary {\r\n    padding: 1.5rem;\r\n  }\r\n\r\n  .formGrid {\r\n    grid-template-columns: 1fr;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .stepActions {\r\n    flex-direction: column-reverse;\r\n  }\r\n\r\n  .nextBtn,\r\n  .placeOrderBtn,\r\n  .backBtn {\r\n    width: 100%;\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .container {\r\n    padding: 6rem 0.5rem 0.5rem;\r\n  }\r\n\r\n  .title {\r\n    font-size: 1.75rem;\r\n  }\r\n\r\n  .mainContent,\r\n  .orderSummary {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .sectionTitle {\r\n    font-size: 1.25rem;\r\n  }\r\n\r\n  .paymentMethod {\r\n    padding: 1rem;\r\n  }\r\n\r\n  .paymentIcon {\r\n    font-size: 1.5rem;\r\n    width: 40px;\r\n  }\r\n\r\n  .orderItem {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    text-align: center;\r\n  }\r\n\r\n  .itemImage {\r\n    align-self: center;\r\n  }\r\n}\r\n\r\n/* Error message styles */\r\n.errorMessage {\r\n  background-color: #fee2e2;\r\n  border: 1px solid #fca5a5;\r\n  border-radius: 6px;\r\n  padding: 12px 16px;\r\n  margin: 16px 0;\r\n}\r\n\r\n.errorMessage p {\r\n  color: #dc2626;\r\n  margin: 0;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* Payment method improvements */\r\n.paymentMethod {\r\n  transition: all 0.2s ease;\r\n  cursor: pointer;\r\n}\r\n\r\n.paymentMethod:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.paymentMethod.selected {\r\n  border-color: #1e3a8a;\r\n  background-color: #f0f4ff;\r\n}\r\n\r\n/* Processing state */\r\n.placeOrderBtn:disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n}\r\n"], "names": [], "mappings": "AACA;;;;;;;AAQA;;;;;;;AAOA;;;;;;;;AASA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;;;AAaA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;;;AAOA;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;AAUA;;;;;;;AAOA;;;;;AAKA;;;;AAIA;;;;;;;AAOA;;;;;;;;AASA;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;;AAWA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;;;;AAOA;;;;;;AAMA;;;;;;AAOA;;;;;;;;AAQA;;;;;;;;;;;;AAaA;;;;;AAMA;;;;;;AAMA;;;;;;;;;;;;AAYA;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;;;AAUA;;;;AAIA;;;;;;;;AAQA;;;;AAIA;;;;;;;AAOA;;;;;AAKA;;;;;;;;AAQA;;;;;;AAMA;;;;;;AAOA;;;;;AAKA;;;;;;;AAOA;;;;;AAKA;;;;;;AAMA;;;;;;;;;AASA;;;;;;AAMA;;;;;;AAOA;EACE;;;;;EAKA;;;;;;AAMF;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;;AAQF;EACE;;;;EAIA;;;;EAIA;;;;EAKA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;EAMA;;;;;AAMF;;;;;;;;AAQA;;;;;;;AAQA;;;;;AAKA;;;;;AAKA;;;;;AAMA", "debugId": null}}]}